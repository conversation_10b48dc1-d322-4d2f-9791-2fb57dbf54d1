import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class SupportDp extends StatelessWidget {
  const SupportDp({
    super.key,
    this.name,
    this.userId,
    this.faceUrl,
    this.size = 55,
  });

  final String? faceUrl;
  final double? size;
  final String? userId;
  final String? name;

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: Container(
        decoration: BoxDecoration(
          gradient: userId.gradientFromUserId,
        ),
        width: size,
        height: size,
        child: faceUrl == null
            ? _buildFallbackWidget(context)
            : CachedNetworkImage(
                imageUrl: faceUrl!,
                fit: BoxFit.cover,
                errorWidget: (context, url, error) => _buildFallbackWidget(context),
              ),
      ),
    );
  }

  Widget _buildFallbackWidget(BuildContext context) {
    return Center(
      child: Icon(
        LucideIcons.headset,
        color: context.theme.scaffoldBackgroundColor,
      ),
    );
  }
}
