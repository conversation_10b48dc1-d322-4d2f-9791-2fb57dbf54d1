import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';

class PositionDropdown extends StatelessWidget {
  const PositionDropdown({
    super.key,
    required this.positions,
    required this.tradeType,
    required this.selectedPosition,
    required this.onPositionSelected,
  });

  final List<OrderRecord> positions;
  final int tradeType;
  final OrderRecord? selectedPosition;
  final ValueChanged<OrderRecord?> onPositionSelected;

  @override
  Widget build(BuildContext context) {
    return AppDropdown<int?>(
      hintText: 'selectPosition'.tr(),
      items: positions.where((e) => e.tradeType == tradeType).map(
        (e) {
          final tradeTypeOption = TradeTypeOption.fromValue(e.tradeType ?? 1);
          final label =
              ' ${e.id} | ${'averagePrice'.tr()} ${e.buyAvgPrice?.toStringAsFixed(2) ?? ''} | ${'total'.tr()} ${e.buyTotalNum ?? ''} | ${'available'.tr()} ${e.restNum ?? ''}';
          return DropdownMenuItem<int?>(
            value: e.id,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 3.0),
                    decoration: BoxDecoration(
                      color: tradeTypeOption.color(context),
                      borderRadius: BorderRadius.circular(2.gr),
                    ),
                    child: Text(
                      TradeTypeOption.fromValue(int.tryParse(e.tradeType?.toString() ?? '')).text ,
                      style: FontPalette.normal9.copyWith(color: Colors.white),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      label,
                      style: FontPalette.normal11.copyWith(
                        color: ColorPalette.titleColor,
                      ),
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ).toList(),
      selected: selectedPosition?.id,
      onChanged: (value) => onPositionSelected(positions.firstWhereOrNull((e) => e.id == value)),
    );
  }
}
