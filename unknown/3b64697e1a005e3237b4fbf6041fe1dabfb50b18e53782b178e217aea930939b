import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class TableEmptyWidget extends StatelessWidget {
  final double? height;
  final double? width;
  final String? title;
  final Color? backgroundColor;
  final EdgeInsets? margin;
  final double? radius;

  const TableEmptyWidget({
    super.key,
    this.height,
    this.width,
    this.title,
    this.backgroundColor,
    this.margin,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: radius != null ? BorderRadius.all(Radius.circular(radius!)) : null,
      ),
      padding: EdgeInsets.symmetric(vertical: 16.gh),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            16.verticalSpace,
            Assets.noItemIcon.svg(
              width ?? 100.gw,
              height ?? 100.gh,
            ),
            16.verticalSpace,
            Text(
              title ?? 'no_data_available'.tr(),
              style: FontPalette.normal14.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
