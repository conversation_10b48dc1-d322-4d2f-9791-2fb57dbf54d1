import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:gp_stock_app/features/chat/screens/chat_screen.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/support_dp.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/empty_chat.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitContact/tim_uikit_contact.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class UsersChats extends StatefulWidget {
  const UsersChats({super.key, this.onMembersCountChange});
  final Function(int count)? onMembersCountChange;
  @override
  UsersChatsState createState() => UsersChatsState();
}

class UsersChatsState extends State<UsersChats> {
  @override
  Widget build(BuildContext context) {
    return TIMUIKitContact(
      titleColor: context.colorTheme.textPrimary,
      chatTileColor: context.theme.scaffoldBackgroundColor,
      onTapItem: (value) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatScreen(
              selectedConversation: getConversation(
                userID: value.userID,
                name: value.userID,
                faceUrl: value.userProfile?.faceUrl,
              ),
              freindInfo: value,
            ),
          ),
        );
      },
      avatarBuilder: (context, faceUrl, userId, name, role) => role == 1 || role == 2
          ? SupportDp(
              faceUrl: faceUrl,
              userId: userId,
              name: name,
            )
          : Dp(
              faceUrl: faceUrl,
              type: 1,
              userId: userId,
              name: name,
            ),
      suffixBuilder: (context, onTap) => Padding(
        padding: const EdgeInsets.only(right: 12),
        child: Icon(
          LucideIcons.send_horizontal,
          color: context.theme.primaryColor.withValues(alpha: 0.5),
          size: 18,
        ),
      ),
      emptyBuilder: (v) => const EmptyChat(),
      onCountChange: widget.onMembersCountChange,
    );
  }
}
