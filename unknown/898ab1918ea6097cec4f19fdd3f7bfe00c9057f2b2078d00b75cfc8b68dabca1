import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import '../../features/home/<USER>/settings_menu.dart';
import '../models/sys_settings_model/sys_settings_model.dart';

mixin AppHeaderMixin {
  Widget buildAppHeader(BuildContext context, {double height = 320, bool showHeaderImage = false}) {
    return BlocSelector<SysSettingsCubit, SysSettingsState, SysSettingsModel?>(
      selector: (state) => state.maybeWhen(
        loaded: (sysSettings) => sysSettings,
        orElse: () => null,
      ),
      builder: (context, sysSettings) {
        final darkMode = isDarkMode(context);
        final logoUrl = (darkMode ? sysSettings?.logoDark : sysSettings?.logoLight) ?? '';
        return SizedBox(
          width: double.infinity,
          height: height,
          child: Stack(
            children: [
              // Background SVG
              Image.asset(
                Assets.loginHeader,
                width: 1.gsw,
                height: height,
                fit: BoxFit.cover,
              ),

              // App Logo and Web Icon
              Positioned(
                top: 50.gh,
                left: 0,
                right: 0,
                child: Stack(
                  // alignment: Alignment.center,
                  children: [
                    if (Navigator.of(context).canPop()) ...[
                      BackButton(),
                    ],
                    // Centered Logo
                    Center(
                      child: CachedNetworkImage(
                        imageUrl: logoUrl,
                        fit: BoxFit.contain,
                        height: 32.gh,
                        errorWidget: (context, url, error) => SizedBox(height: 32, width: 32),
                      ),
                    ),

                    // Web Icon positioned on the right
                    Positioned(
                      right: 14.gw,
                      child: IconButton(
                        onPressed: () {
                          _showSubMenu(context: context, child: LanguageOptions());
                        },
                        icon: SvgPicture.asset(
                          Assets.webIcon,
                          width: 24.gw,
                          height: 24.gh,
                        ),
                        iconSize: 24.gw,
                        padding: EdgeInsets.all(8.gw),
                      ),
                    ),
                  ],
                ),
              ),

              // Login Header and Image
              if (showHeaderImage)
                Positioned(
                  top: 100.gh,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: SizedBox(
                      width: 0.65.gsw,
                      child: Image.asset(
                        Assets.loginHeader2,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                )
            ],
          ),
        );
      },
    );
  }

  Future<void> _showSubMenu({
    required BuildContext context,
    required Widget child,
  }) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);

    await showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        buttonPosition.dx,
        buttonPosition.dy + 80.gh,
        buttonPosition.dx - 1.gh,
        0,
      ),
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 120.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            width: 160.gw,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gr),
              boxShadow: [
                BoxShadow(
                  color: context.theme.shadowColor,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ],
    );
  }
}
