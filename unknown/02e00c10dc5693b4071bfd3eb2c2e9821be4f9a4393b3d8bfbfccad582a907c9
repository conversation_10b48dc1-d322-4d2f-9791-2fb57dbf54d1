import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class BuildActionButton extends StatelessWidget {
  const BuildActionButton({
    super.key,
    required this.label,
    required this.icon,
    this.onTap,
  });

  final String label;
  final String icon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconHelper.loadAsset(
            icon,
            width: 30.gw,
            height: 30.gh,
          ),
          <PERSON>zed<PERSON>ox(height: 4.gh),
          SizedBox(
            width: 80.gw,
            child: Text(
              label,
              style: FontPalette.medium11.copyWith(
                color: context.colorTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
