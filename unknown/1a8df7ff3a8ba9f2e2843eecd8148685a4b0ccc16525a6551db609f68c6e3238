import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockPriceSection extends StatelessWidget {
  const StockPriceSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TradingCubit, TradingState>(
      builder: (context, state) {
        if (state.stockInfo == null || state.marketDepth == null) {
          return ShimmerWidget(height: 100.gh);
        }
        return Column(
          children: [
            _buildStockPriceSection(context, stockInfo: state.stockInfo),
            16.verticalSpace,
            // Buy/Sell Distribution Bar
            // _buildBuySellDistribution(stockInfo: state.stockInfo, marketDepth: state.marketDepth),
          ],
        );
      },
    );
  }

  //  Widget _buildBuySellDistribution({required StockInfoData? stockInfo, required MarketDepthData? marketDepth}) {
  //   final buy = marketDepth?.bid?[0].vol ?? 0;
  //   final sell = marketDepth?.ask?[0].vol ?? 0;
  //   final buyPer = (buy / (buy + sell) * 100);
  //   final sellPer = (100 - buyPer);

  //   logDev('buyPercent', buyPer.toString());

  //   return Column(
  //     children: [
  //       Row(
  //         children: [
  //           Expanded(
  //               flex: 12,
  //               child: Column(
  //                 children: [
  //                   Text(
  //                     '卖盘',
  //                     style: FontPalette.normal12.copyWith(color: ColorPalette.textColor2),
  //                   ),
  //                   5.verticalSpace,
  //                   Text(
  //                     '${sellPer.toStringAsFixed(2)}%',
  //                     style: FontPalette.semiBold10
  //                         .copyWith(color: ColorPalette.greenColor, fontFamily: 'Akzidenz-Grotesk'),
  //                   ),
  //                 ],
  //               )),
  //           Expanded(
  //             flex: 90,
  //             child: Container(
  //               height: 10.gh,
  //               decoration: BoxDecoration(
  //                 borderRadius: BorderRadius.circular(3),
  //               ),
  //               child: LayoutBuilder(
  //                 builder: (context, constraints) {
  //                   return Row(
  //                     children: [
  //                       Expanded(
  //                         flex: sellPer.round(),
  //                         child: Container(
  //                           height: 10.gh,
  //                           decoration: BoxDecoration(
  //                             color: ColorPalette.greenColor,
  //                             borderRadius: BorderRadius.horizontal(
  //                               left: Radius.circular(10),
  //                               right: sellPer == 1 ? Radius.circular(10) : Radius.zero,
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                       Expanded(
  //                         flex: buyPer.round(),
  //                         child: Container(
  //                           height: 10.gh,
  //                           decoration: BoxDecoration(
  //                             color: ColorPalette.redColor,
  //                             borderRadius: BorderRadius.horizontal(
  //                               right: Radius.circular(10),
  //                               left: buyPer == 1 ? Radius.circular(10) : Radius.zero,
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                     ],
  //                   );
  //                 },
  //               ),
  //             ),
  //           ),
  //           Expanded(
  //               flex: 12,
  //               child: Column(
  //                 children: [
  //                   Text(
  //                     '买盘',
  //                     style: FontPalette.normal12.copyWith(color: ColorPalette.textColor2),
  //                   ),
  //                   5.verticalSpace,
  //                   Text(
  //                     '${buyPer.toStringAsFixed(2)}%',
  //                     style:
  //                         FontPalette.semiBold10.copyWith(color: ColorPalette.redColor, fontFamily: 'Akzidenz-Grotesk'),
  //                   ),
  //                 ],
  //               )),
  //         ],
  //       ),
  //     ],
  //   );
  // }

  Widget _buildStockPriceSection(BuildContext context, {required StockInfoData? stockInfo}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${stockInfo?.name} (${stockInfo?.symbol})',
              style: FontPalette.semiBold15,
            ),
            5.verticalSpace,
            Row(
              children: [
                AnimatedFlipCounter(
                  fractionDigits: 2,
                  thousandSeparator: ',',
                  textStyle: FontPalette.bold17.copyWith(
                    color: ColorPalette.greenColor,
                    fontFamily: 'Akzidenz-Grotesk',
                  ),
                  value: stockInfo?.latestPrice ?? 0,
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: ColorPalette.greenColor,
                ),
              ],
            ),
            5.verticalSpace,
            Text(
              '${stockInfo?.chg} ${stockInfo?.gain}%',
              style: FontPalette.normal14.copyWith(color: ColorPalette.greenColor, fontFamily: 'Akzidenz-Grotesk'),
            ),
          ],
        ),
        TextButton(
          onPressed: () {},
          child: Text(
            '详情',
            style: FontPalette.normal14.copyWith(color: context.theme.primaryColor ),
          ),
        ),
      ],
    );
  }
}
