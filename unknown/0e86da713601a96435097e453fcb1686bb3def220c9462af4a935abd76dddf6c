import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeBuySellDialog extends StatelessWidget {
  final List<({String label, String value, Color? valueColor, String? currency, bool showTotalToolTip})> confirmList;
  final VoidCallback submit;
  const FTradeBuySellDialog({
    super.key,
    required this.confirmList,
    required this.submit,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                "orderConfirmation".tr(),
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 16),
            ...confirmList.indexed.expand((entry) {
              final int index = entry.$1;
              final item = entry.$2;
              return [
                _BuildRow(
                  label: item.label,
                  value: item.value,
                  valueColor: item.valueColor,
                  currency: item.currency,
                  showTotalToolTip: item.showTotalToolTip,
                ),
                index == confirmList.length - 2 ? SizedBox(height: 16) : SizedBox.shrink()
              ];
            }),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildButton(
                  context,
                  text: "cancel".tr(),
                  color: context.theme.cardColor,
                  borderColor: context.theme.dividerColor,
                  onPressed: () => Navigator.pop(context),
                ),
                _buildButton(
                  context,
                  text: "submit".tr(),
                  textColor: context.theme.cardColor,
                  onPressed: () {
                    submit();
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(
    BuildContext context, {
    required String text,
    Color? color,
    Color? borderColor,
    Color? textColor,
    required VoidCallback onPressed,
  }) {
    return Expanded(
        child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: CustomMaterialButton(
        onPressed: onPressed,
        buttonText: text,
        color: color,
        borderColor: borderColor ?? context.theme.primaryColor,
        borderRadius: 5.gr,
        fontSize: 13.gr,
        textColor: textColor,
        isOutLined: textColor == null,
      ),
    ));
  }
}

class _BuildRow extends StatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;
  final String? currency;
  final bool showTotalToolTip;

  const _BuildRow({
    required this.label,
    required this.value,
    this.valueColor,
    this.currency,
    this.showTotalToolTip = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            spacing: 10,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 14, color: context.colorTheme.textPrimary),
              ),
              if (showTotalToolTip)
                GestureDetector(
                  onTap: () => showToolTip(context),
                  child: Icon(
                    Icons.help_outline,
                    color: context.theme.primaryColor,
                    size: 14.gsp,
                  ),
                ),
            ],
          ),
          if (currency != null)
            AnimatedFlipCounter(
              duration: const Duration(milliseconds: 500),
              suffix: currency == null ? '' : ' $currency',
              thousandSeparator: ',',
              fractionDigits: 2,
              textStyle: FontPalette.extraBold14.copyWith(
                color: context.theme.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
                height: 1,
                fontSize: 14,
              ),
              value: double.tryParse(value) ?? 0,
            )
          else
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: valueColor ?? context.theme.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
              ),
            ),
        ],
      ),
    );
  }
}
