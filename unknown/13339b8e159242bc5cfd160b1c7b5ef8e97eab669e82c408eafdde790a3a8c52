import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class KeyboardConfig {
  static KeyboardActionsConfig buildConfig(BuildContext context, FocusNode focusNode) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.ALL,
      keyboardBarColor: context.theme.scaffoldBackgroundColor,
      nextFocus: true,
      actions: [
        if (Platform.isIOS)
          KeyboardActionsItem(
            focusNode: focusNode,
            displayArrows: false,
            toolbarButtons: [
              (node) {
                return GestureDetector(
                  onTap: () => node.unfocus(),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.0.gw, vertical: 8.0.gh),
                    child: Text(
                      "done".tr(),
                      style: context.textTheme.regular,
                    ),
                  ),
                );
              }
            ],
          ),
      ],
    );
  }
}
