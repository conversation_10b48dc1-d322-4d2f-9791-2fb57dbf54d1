import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';

import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/routes/routes.dart';
import '../widgets/build_action_buttons.dart';

class ActionButtonsRowType1 extends StatelessWidget {
  const ActionButtonsRowType1({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        BuildActionButton(
          label: 'topUpDeposit'.tr(),
          icon: Assets.myAssetIcon,
          onTap: () {
            context.verifyAuth(
              () => Navigator.pushNamed(context, routeDepositMain),
            );
          },
        ),
        BuildActionButton(
          label: 'cashOut'.tr(),
          icon: Assets.withdrawIcon,
          onTap: () {
            context.verifyAuth(
              () => context.verifyRealName(
                () => Navigator.pushNamed(context, routeWithdrawMain),
              ),
            );
          },
        ),
      ],
    );
  }
}

class ActionButtonsRowType2 extends StatelessWidget {
  const ActionButtonsRowType2({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        BuildActionButton(
          label: 'applyContract2'.tr(),
          icon: Assets.contract1Icon,
          onTap: () {
            context.verifyAuth(() async {
              await Navigator.pushNamed(
                context,
                routeContractApply,
                arguments: {'mainContractType': MainContractType.stock},
              );
            });
          },
        ),
        BuildActionButton(
          label: 'apply_records'.tr(),
          icon: Assets.contract2Icon,
          onTap: () => context.verifyRealName(() => Navigator.pushNamed(context, routeContractApplyRecord)),
        ),
        BuildActionButton(
          label: 'historicalMessages'.tr(),
          icon: Assets.contract3Icon,
          onTap: () => context.verifyRealName(() => Navigator.pushNamed(context, routeContractSettleHistory)),
        ),
      ],
    );
  }
}
