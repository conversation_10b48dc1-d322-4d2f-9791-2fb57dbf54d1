import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class PositionWidget extends StatelessWidget {
  const PositionWidget({super.key, required this.data, required this.onTap});

  final OrderRecord data;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(10.gh),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            spacing: 10.gh,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 4.gh,
                children: [
                  SymbolChip(
                    name: TradeTypeOption.fromValue(int.tryParse(data.tradeType?.toString() ?? '')).text ,
                    chipColor:
                        TradeTypeOption.fromValue(int.tryParse(data.tradeType?.toString() ?? '')).color(context),
                  ),
                  SymbolChip(
                    name: 'openSymbol'.tr(),
                    chipColor: context.upColor,
                  ),
                  SymbolChip(name: data.market?.substring(0, 2) ?? '', chipColor: context.theme.primaryColor ),
                  Text(data.symbolName ?? '', style: FontPalette.semiBold12),
                  Text(
                    '(${data.currency ?? ''})',
                    style: FontPalette.light13.copyWith(color: context.colorTheme.textRegular),
                  ),
                  Text(
                    '(${data.id ?? ''})',
                    style: FontPalette.light13.copyWith(color: context.colorTheme.textRegular),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: _dataField(context,
                      'available'.tr(),
                      data.restNum ?? 0,
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: _dataField(context,
                        'currentPrice'.tr(),
                        data.stockPrice ?? 0,
                        fractionDigits: 3,
                        isCenter: true,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _dataField(context,
                      'profitLoss'.tr(),
                      data.floatingProfitLoss ?? 0,
                      isRight: true,
                      showSymbol: true,
                      color: (data.floatingProfitLoss ?? 0) > 0 ? context.upColor : null,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: _dataField(context,
                      'totalQuantity'.tr(),
                      data.buyTotalNum ?? 0,
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: _dataField(context,
                        'averagePrice'.tr(),
                        data.buyAvgPrice ?? 0,
                        fractionDigits: 3,
                        isCenter: true,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _dataField(context,
                      'marketValue'.tr(),
                      data.marketValue ?? 0,
                      isRight: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _dataField(
      BuildContext context,
    String title,
    double value, {
    bool isRight = false,
    bool isCenter = false,
    int fractionDigits = 2,
    bool showSymbol = false,
    Color? color,
  }) {
    return Column(
      crossAxisAlignment: isRight
          ? CrossAxisAlignment.end
          : isCenter
              ? CrossAxisAlignment.center
              : CrossAxisAlignment.start,
      children: [
        Text(title, style: FontPalette.normal12.copyWith(color: ColorPalette.textColor2)),
        AnimatedFlipCounter(
          fractionDigits: fractionDigits,
          textStyle: FontPalette.bold14.copyWith(
            color: color ?? context.theme.primaryColor,
            fontFamily: 'Akzidenz-Grotesk',
          ),
          value: value,
          prefix: showSymbol
              ? value > 0
                  ? '+'
                  : ''
              : '',
          thousandSeparator: ',',
        )
      ],
    );
  }
}
