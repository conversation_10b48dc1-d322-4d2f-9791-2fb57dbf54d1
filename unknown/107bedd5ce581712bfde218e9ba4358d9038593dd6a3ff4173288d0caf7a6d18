import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/domain/models/tick_list_response.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class TickListSection extends StatefulWidget {
  final Instrument instrument;
  final Color Function(double value)? getColorCallback;
  const TickListSection({
    super.key,
    required this.instrument,
    this.getColorCallback,
  });

  @override
  State<TickListSection> createState() => _TickListSectionState();
}

class _TickListSectionState extends State<TickListSection> {
  final controller = ScrollController();

  @override
  void initState() {
    super.initState();
    controller.addListener(_onScroll);
  }

  void _onScroll() {
    if (controller.position.pixels == controller.position.maxScrollExtent) {
      context.read<TradingCubit>().getTickList(instrument: widget.instrument.instrument);
    }
  }

  @override
  void dispose() {
    controller.removeListener(_onScroll);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 0.32.gsh,
      color: context.theme.cardColor,
      padding: EdgeInsets.fromLTRB(0, 0, 8, 0),
      child: BlocSelector<TradingCubit, TradingState, ({TickListResponse? tickList, DataStatus status})>(
        selector: (state) => (tickList: state.tickList, status: state.tickListStatus),
        builder: (context, state) {
          if (state.status == DataStatus.loading && state.tickList == null) {
            return ShimmerWidget(height: 100.gh);
          }
          if (state.status == DataStatus.failed || state.tickList == null) {
            return Center(child: TableEmptyWidget());
          }
          final items = state.tickList?.data?.records ?? <TickRecord>[];
          if (items.isEmpty) return Center(child: TableEmptyWidget());
          return ListView.builder(
            itemCount: items.length + 1,
            controller: controller,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              if (index >= items.length) {
                if (state.tickList?.data?.hasNext ?? false) {
                  return ShimmerWidget(height: 10.gh);
                }
                return const SizedBox.shrink();
              }
              final item = items[index];
              final time = (item.time != null)
                  ? TimeZoneHelper.formatTimeInZone(
                      DateTime.parse(item.time!).millisecondsSinceEpoch ~/ 1000,
                      'Asia/Shanghai', //!check here, should be dynamic
                      format: TimeFormat.hm,
                    )
                  : '--:--';
              final tradePrice = '${item.tradePrice}';

              final tradeVolume = formatLargeNumber(item.tradeVolume?.toDouble() ?? 0.0, context.locale.languageCode);

              final direction = '${item.direction}';
              final directionColor = {
                    'S': Colors.greenAccent.shade700,
                    'B': Colors.red,
                    'N': Colors.grey,
                  }[item.direction] ??
                  Colors.grey;

              return SingleChildScrollView(
                scrollDirection: Axis.horizontal, //! check performance
                child: Row(
                  spacing: 8,
                  children: [
                    Text(
                      time,
                      style: FontPalette.normal8,
                    ),
                    Text(
                      tradePrice,
                      style: FontPalette.normal8
                          .copyWith(color: widget.getColorCallback?.call(double.parse(tradePrice)) ?? Colors.white),
                    ),
                    Row(
                      spacing: 4,
                      children: [
                        Text(
                          tradeVolume,
                          style: FontPalette.normal8,
                        ),
                        Text(
                          direction,
                          style: SecFontPalette.normal8.copyWith(color: directionColor),
                        ),
                      ],
                    )
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
