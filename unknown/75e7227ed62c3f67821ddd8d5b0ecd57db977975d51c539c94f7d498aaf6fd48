import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/features/company_news/logic/news/company_news_cubit.dart';

import '../../../shared/constants/enums.dart';
import '../logic/news/company_news_state.dart';
import '../widgets/company_news_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class CompanyNewsDetailsScreen extends StatefulWidget {
  final String newsId;

  const CompanyNewsDetailsScreen({super.key, required this.newsId});

  @override
  State<CompanyNewsDetailsScreen> createState() => _CompanyNewsDetailsScreenState();
}

class _CompanyNewsDetailsScreenState extends State<CompanyNewsDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(
          color: context.colorTheme.textPrimary,
        ),
        title: Text(
          'newsDetails'.tr(),
          style: context.textTheme.primary.fs16.w500,
        ),
      ),
      body: BlocBuilder<CompanyNewsCubit, CompanyNewsDetailsState>(
        builder: (context, state) {
          if (state.status == DataStatus.loading) {
            return Center(
              child: CircularProgressIndicator(
                color: context.theme.primaryColor,
              ),
            );
          }

          if (state.status == DataStatus.failed) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.error ?? 'Failed to load news',
                    style: context.textTheme.regular,
                  ),
                  TextButton(
                    onPressed: () => context.read<CompanyNewsCubit>().getCompanyNewsDetails(widget.newsId),
                    child: Text('retry'.tr()),
                  ),
                ],
              ),
            );
          }

          final newsDetails = state.newsDetails;
          if (newsDetails == null) {
            return Center(
              child: Text(
                'noData'.tr(),
                style: context.textTheme.regular,
              ),
            );
          }

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CompanyNewsHeader(newsDetails: newsDetails),
                CompanyNewsContent(newsDetails: newsDetails),
                // _RelatedNews(),
              ],
            ),
          );
        },
      ),
    );
  }
}
