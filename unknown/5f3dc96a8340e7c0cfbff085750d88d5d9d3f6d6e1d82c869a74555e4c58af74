import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:gp_stock_app/features/chat/utils/chat_time_ago.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ChatTileHeading extends StatelessWidget {
  const ChatTileHeading({
    super.key,
    required this.data,
  });

  final V2TimConversation data;

  @override
  Widget build(BuildContext context) {
    final isDisturb = (data.groupType == "Meeting" ? false : data.recvOpt != 0);
    final lastTime = ChatTimeAgo.getTimeStringForChat(data.c2cReadTimestamp ?? 0);
    V2TimFriendInfo? freindInfo = data.friendInfo;
    return Row(
      spacing: 6,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: Row(
            spacing: 10,
            children: [
              Text(
                freindInfo?.userProfile?.nickName ?? data.showName ?? '',
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                softWrap: false,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isDisturb)
                const Icon(
                  LucideIcons.bell_off,
                  size: 14,
                  color: Colors.black26,
                ),
              if (freindInfo != null && freindInfo.userProfile?.role == 1) const SupportTag.general(),
              if (freindInfo != null && freindInfo.userProfile?.role == 2) const SupportTag.financial()
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                lastTime ?? '',
                style: context.textTheme.regular.fs12,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class SupportTag extends StatelessWidget {
  const SupportTag.general({
    super.key,
    this.color = Colors.green,
    this.label = 'chatGeneral',
  });

  const SupportTag.financial({
    super.key,
    this.color = Colors.red,
    this.label = 'financial',
  });

  final Color color;
  final String label;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label.tr(),
        style: const TextStyle(
          fontSize: 12,
          color: Colors.white,
        ),
      ),
    );
  }
}
