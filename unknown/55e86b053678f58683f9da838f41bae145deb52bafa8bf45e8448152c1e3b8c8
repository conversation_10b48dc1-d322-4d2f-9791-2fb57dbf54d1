import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';

import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';

import 'package:gp_stock_app/shared/models/instrument/instrument.dart';


FTradeAcctOrderModel $FTradeAcctOrderModelFromJson(Map<String, dynamic> json) {
  final FTradeAcctOrderModel fTradeAcctOrderModel = FTradeAcctOrderModel();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    fTradeAcctOrderModel.current = current;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    fTradeAcctOrderModel.hasNext = hasNext;
  }
  final List<FTradeAcctOrderRecords>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<FTradeAcctOrderRecords>(e) as FTradeAcctOrderRecords).toList();
  if (records != null) {
    fTradeAcctOrderModel.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    fTradeAcctOrderModel.total = total;
  }
  return fTradeAcctOrderModel;
}

Map<String, dynamic> $FTradeAcctOrderModelToJson(FTradeAcctOrderModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension FTradeAcctOrderModelExtension on FTradeAcctOrderModel {
  FTradeAcctOrderModel copyWith({
    int? current,
    bool? hasNext,
    List<FTradeAcctOrderRecords>? records,
    int? total,
  }) {
    return FTradeAcctOrderModel()
      ..current = current ?? this.current
      ..hasNext = hasNext ?? this.hasNext
      ..records = records ?? this.records
      ..total = total ?? this.total;
  }
}

FTradeAcctOrderRecords $FTradeAcctOrderRecordsFromJson(Map<String, dynamic> json) {
  final FTradeAcctOrderRecords fTradeAcctOrderRecords = FTradeAcctOrderRecords();
  final double? buyAvgPrice = jsonConvert.convert<double>(json['buyAvgPrice']);
  if (buyAvgPrice != null) {
    fTradeAcctOrderRecords.buyAvgPrice = buyAvgPrice;
  }
  final double? buyTotalNum = jsonConvert.convert<double>(json['buyTotalNum']);
  if (buyTotalNum != null) {
    fTradeAcctOrderRecords.buyTotalNum = buyTotalNum;
  }
  final double? positionTotalNum = jsonConvert.convert<double>(json['positionTotalNum']);
  if (positionTotalNum != null) {
    fTradeAcctOrderRecords.positionTotalNum = positionTotalNum;
  }
  final double? costPrice = jsonConvert.convert<double>(json['costPrice']);
  if (costPrice != null) {
    fTradeAcctOrderRecords.costPrice = costPrice;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    fTradeAcctOrderRecords.createTime = createTime;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    fTradeAcctOrderRecords.currency = currency;
  }
  final int? direction = jsonConvert.convert<int>(json['direction']);
  if (direction != null) {
    fTradeAcctOrderRecords.direction = direction;
  }
  final int? tradeType = jsonConvert.convert<int>(json['tradeType']);
  if (tradeType != null) {
    fTradeAcctOrderRecords.tradeType = tradeType;
  }
  final double? disableNum = jsonConvert.convert<double>(json['disableNum']);
  if (disableNum != null) {
    fTradeAcctOrderRecords.disableNum = disableNum;
  }
  final double? feeAmount = jsonConvert.convert<double>(json['feeAmount']);
  if (feeAmount != null) {
    fTradeAcctOrderRecords.feeAmount = feeAmount;
  }
  final double? marginAmount = jsonConvert.convert<double>(json['marginAmount']);
  if (marginAmount != null) {
    fTradeAcctOrderRecords.marginAmount = marginAmount;
  }
  final double? floatingProfitLoss = jsonConvert.convert<double>(json['floatingProfitLoss']);
  if (floatingProfitLoss != null) {
    fTradeAcctOrderRecords.floatingProfitLoss = floatingProfitLoss;
  }
  final double? floatingProfitLossRate = jsonConvert.convert<double>(json['floatingProfitLossRate']);
  if (floatingProfitLossRate != null) {
    fTradeAcctOrderRecords.floatingProfitLossRate = floatingProfitLossRate;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    fTradeAcctOrderRecords.id = id;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeAcctOrderRecords.market = market;
  }
  final double? marketValue = jsonConvert.convert<double>(json['marketValue']);
  if (marketValue != null) {
    fTradeAcctOrderRecords.marketValue = marketValue;
  }
  final double? restNum = jsonConvert.convert<double>(json['restNum']);
  if (restNum != null) {
    fTradeAcctOrderRecords.restNum = restNum;
  }
  final double? availableMargin = jsonConvert.convert<double>(json['availableMargin']);
  if (availableMargin != null) {
    fTradeAcctOrderRecords.availableMargin = availableMargin;
  }
  final String? securityType = jsonConvert.convert<String>(json['securityType']);
  if (securityType != null) {
    fTradeAcctOrderRecords.securityType = securityType;
  }
  final double? stockPrice = jsonConvert.convert<double>(json['stockPrice']);
  if (stockPrice != null) {
    fTradeAcctOrderRecords.stockPrice = stockPrice;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeAcctOrderRecords.symbol = symbol;
  }
  final String? symbolName = jsonConvert.convert<String>(json['symbolName']);
  if (symbolName != null) {
    fTradeAcctOrderRecords.symbolName = symbolName;
  }
  final double? tradeUnit = jsonConvert.convert<double>(json['tradeUnit']);
  if (tradeUnit != null) {
    fTradeAcctOrderRecords.tradeUnit = tradeUnit;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    fTradeAcctOrderRecords.type = type;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    fTradeAcctOrderRecords.status = status;
  }
  final double? tradePrice = jsonConvert.convert<double>(json['tradePrice']);
  if (tradePrice != null) {
    fTradeAcctOrderRecords.tradePrice = tradePrice;
  }
  final double? dealNum = jsonConvert.convert<double>(json['dealNum']);
  if (dealNum != null) {
    fTradeAcctOrderRecords.dealNum = dealNum;
  }
  final double? tradeNum = jsonConvert.convert<double>(json['tradeNum']);
  if (tradeNum != null) {
    fTradeAcctOrderRecords.tradeNum = tradeNum;
  }
  final String? cancelTime = jsonConvert.convert<String>(json['cancelTime']);
  if (cancelTime != null) {
    fTradeAcctOrderRecords.cancelTime = cancelTime;
  }
  final double? dealPrice = jsonConvert.convert<double>(json['dealPrice']);
  if (dealPrice != null) {
    fTradeAcctOrderRecords.dealPrice = dealPrice;
  }
  final String? dealTime = jsonConvert.convert<String>(json['dealTime']);
  if (dealTime != null) {
    fTradeAcctOrderRecords.dealTime = dealTime;
  }
  final int? priceType = jsonConvert.convert<int>(json['priceType']);
  if (priceType != null) {
    fTradeAcctOrderRecords.priceType = priceType;
  }
  final String? tradeTime = jsonConvert.convert<String>(json['tradeTime']);
  if (tradeTime != null) {
    fTradeAcctOrderRecords.tradeTime = tradeTime;
  }
  final double? winAmount = jsonConvert.convert<double>(json['winAmount']);
  if (winAmount != null) {
    fTradeAcctOrderRecords.winAmount = winAmount;
  }
  final double? tradeFee = jsonConvert.convert<double>(json['tradeFee']);
  if (tradeFee != null) {
    fTradeAcctOrderRecords.tradeFee = tradeFee;
  }
  final int? contractId = jsonConvert.convert<int>(json['contractId']);
  if (contractId != null) {
    fTradeAcctOrderRecords.contractId = contractId;
  }
  final int? contractType = jsonConvert.convert<int>(json['contractType']);
  if (contractType != null) {
    fTradeAcctOrderRecords.contractType = contractType;
  }
  final int? periodType = jsonConvert.convert<int>(json['periodType']);
  if (periodType != null) {
    fTradeAcctOrderRecords.periodType = periodType;
  }
  final int? multiple = jsonConvert.convert<int>(json['multiple']);
  if (multiple != null) {
    fTradeAcctOrderRecords.multiple = multiple;
  }
  final int? contractAccountId = jsonConvert.convert<int>(json['contractAccountId']);
  if (contractAccountId != null) {
    fTradeAcctOrderRecords.contractAccountId = contractAccountId;
  }
  final double? transactionAmount = jsonConvert.convert<double>(json['transactionAmount']);
  if (transactionAmount != null) {
    fTradeAcctOrderRecords.transactionAmount = transactionAmount;
  }
  final String? productCode = jsonConvert.convert<String>(json['productCode']);
  if (productCode != null) {
    fTradeAcctOrderRecords.productCode = productCode;
  }
  return fTradeAcctOrderRecords;
}

Map<String, dynamic> $FTradeAcctOrderRecordsToJson(FTradeAcctOrderRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['buyAvgPrice'] = entity.buyAvgPrice;
  data['buyTotalNum'] = entity.buyTotalNum;
  data['positionTotalNum'] = entity.positionTotalNum;
  data['costPrice'] = entity.costPrice;
  data['createTime'] = entity.createTime;
  data['currency'] = entity.currency;
  data['direction'] = entity.direction;
  data['tradeType'] = entity.tradeType;
  data['disableNum'] = entity.disableNum;
  data['feeAmount'] = entity.feeAmount;
  data['marginAmount'] = entity.marginAmount;
  data['floatingProfitLoss'] = entity.floatingProfitLoss;
  data['floatingProfitLossRate'] = entity.floatingProfitLossRate;
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['marketValue'] = entity.marketValue;
  data['restNum'] = entity.restNum;
  data['availableMargin'] = entity.availableMargin;
  data['securityType'] = entity.securityType;
  data['stockPrice'] = entity.stockPrice;
  data['symbol'] = entity.symbol;
  data['symbolName'] = entity.symbolName;
  data['tradeUnit'] = entity.tradeUnit;
  data['type'] = entity.type;
  data['status'] = entity.status;
  data['tradePrice'] = entity.tradePrice;
  data['dealNum'] = entity.dealNum;
  data['tradeNum'] = entity.tradeNum;
  data['cancelTime'] = entity.cancelTime;
  data['dealPrice'] = entity.dealPrice;
  data['dealTime'] = entity.dealTime;
  data['priceType'] = entity.priceType;
  data['tradeTime'] = entity.tradeTime;
  data['winAmount'] = entity.winAmount;
  data['tradeFee'] = entity.tradeFee;
  data['contractId'] = entity.contractId;
  data['contractType'] = entity.contractType;
  data['periodType'] = entity.periodType;
  data['multiple'] = entity.multiple;
  data['contractAccountId'] = entity.contractAccountId;
  data['transactionAmount'] = entity.transactionAmount;
  data['productCode'] = entity.productCode;
  return data;
}

extension FTradeAcctOrderRecordsExtension on FTradeAcctOrderRecords {
  FTradeAcctOrderRecords copyWith({
    double? buyAvgPrice,
    double? buyTotalNum,
    double? positionTotalNum,
    double? costPrice,
    String? createTime,
    String? currency,
    int? direction,
    int? tradeType,
    double? disableNum,
    double? feeAmount,
    double? marginAmount,
    double? floatingProfitLoss,
    double? floatingProfitLossRate,
    int? id,
    String? market,
    double? marketValue,
    double? restNum,
    double? availableMargin,
    String? securityType,
    double? stockPrice,
    String? symbol,
    String? symbolName,
    double? tradeUnit,
    int? type,
    int? status,
    double? tradePrice,
    double? dealNum,
    double? tradeNum,
    String? cancelTime,
    double? dealPrice,
    String? dealTime,
    int? priceType,
    String? tradeTime,
    double? winAmount,
    double? tradeFee,
    int? contractId,
    int? contractType,
    int? periodType,
    int? multiple,
    int? contractAccountId,
    double? transactionAmount,
    String? productCode,
  }) {
    return FTradeAcctOrderRecords()
      ..buyAvgPrice = buyAvgPrice ?? this.buyAvgPrice
      ..buyTotalNum = buyTotalNum ?? this.buyTotalNum
      ..positionTotalNum = positionTotalNum ?? this.positionTotalNum
      ..costPrice = costPrice ?? this.costPrice
      ..createTime = createTime ?? this.createTime
      ..currency = currency ?? this.currency
      ..direction = direction ?? this.direction
      ..tradeType = tradeType ?? this.tradeType
      ..disableNum = disableNum ?? this.disableNum
      ..feeAmount = feeAmount ?? this.feeAmount
      ..marginAmount = marginAmount ?? this.marginAmount
      ..floatingProfitLoss = floatingProfitLoss ?? this.floatingProfitLoss
      ..floatingProfitLossRate = floatingProfitLossRate ?? this.floatingProfitLossRate
      ..id = id ?? this.id
      ..market = market ?? this.market
      ..marketValue = marketValue ?? this.marketValue
      ..restNum = restNum ?? this.restNum
      ..availableMargin = availableMargin ?? this.availableMargin
      ..securityType = securityType ?? this.securityType
      ..stockPrice = stockPrice ?? this.stockPrice
      ..symbol = symbol ?? this.symbol
      ..symbolName = symbolName ?? this.symbolName
      ..tradeUnit = tradeUnit ?? this.tradeUnit
      ..type = type ?? this.type
      ..status = status ?? this.status
      ..tradePrice = tradePrice ?? this.tradePrice
      ..dealNum = dealNum ?? this.dealNum
      ..tradeNum = tradeNum ?? this.tradeNum
      ..cancelTime = cancelTime ?? this.cancelTime
      ..dealPrice = dealPrice ?? this.dealPrice
      ..dealTime = dealTime ?? this.dealTime
      ..priceType = priceType ?? this.priceType
      ..tradeTime = tradeTime ?? this.tradeTime
      ..winAmount = winAmount ?? this.winAmount
      ..tradeFee = tradeFee ?? this.tradeFee
      ..contractId = contractId ?? this.contractId
      ..contractType = contractType ?? this.contractType
      ..periodType = periodType ?? this.periodType
      ..multiple = multiple ?? this.multiple
      ..contractAccountId = contractAccountId ?? this.contractAccountId
      ..transactionAmount = transactionAmount ?? this.transactionAmount
      ..productCode = productCode ?? this.productCode;
  }
}