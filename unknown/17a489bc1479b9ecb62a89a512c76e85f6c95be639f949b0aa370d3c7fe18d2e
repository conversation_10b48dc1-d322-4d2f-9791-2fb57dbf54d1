import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/auto_scrolling_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:html/parser.dart' as html_parser;

import '../../../core/utils/app_navigation_handler.dart';
import '../logic/home_notification_cubit/home_notification_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class HomeMarqueeText extends StatefulWidget {
  const HomeMarqueeText({super.key});

  @override
  State<HomeMarqueeText> createState() => _HomeMarqueeTextState();
}

class _HomeMarqueeTextState extends State<HomeMarqueeText> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      child: BlocConsumer<HomeNotificationCubit, List<HomeNotificationModel>>(
        listener: (context, state) {
          if (context.read<HomeNotificationCubit>().shouldSuppressPopup) {
            return;
          }

          final notifications = state.where((e) => e.type == 3).toList();
          if (notifications.isNotEmpty) {
            // Preload images before showing the dialog
            for (var notification in notifications) {
              if (notification.imageUrl.isNullOrEmpty) continue;
              precacheImage(
                CachedNetworkImageProvider(notification.imageUrl),
                context,
              );
            }
            WidgetsBinding.instance.addPostFrameCallback((_) {
              showDialog(
                context: context,
                builder: (context) => NotificationPopup(
                  notifications: notifications,
                ),
              );
            });
          }
        },
        builder: (context, state) {
          return _buildMarqueeContainer(context, state);
        },
      ),
    );
  }

  Widget _buildMarqueeContainer(BuildContext context, List<HomeNotificationModel> state) {
    // Get notifications of type 2
    final typeNotifications = state.where((e) => e.type == 2).toList();

    // If there are no notifications, don't show the container at all
    if (typeNotifications.isEmpty) {
      return const SizedBox.shrink(); // Return an empty widget
    }

    return Container(
      height: 40.gw,
      margin: EdgeInsets.only(top: 5.gw),
      padding: EdgeInsets.symmetric(horizontal: 13.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gw),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 1.gw),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              Assets.soundUpIcon,
              width: 20.gw,
              height: 20.gh,
              colorFilter: ColorFilter.mode(
                context.theme.primaryColor,
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 6.gw),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 2.gw),
                child: _buildMarqueeText(context, typeNotifications),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the marquee text widget with proper error handling
  /// The notifications passed here are already filtered for type 2
  Widget _buildMarqueeText(BuildContext context, List<HomeNotificationModel> notifications) {
    // Create the text content from notifications
    final String content = notifications.map((notification) => notification.content).join(' | ');

    // Parse HTML content if needed
    final String displayText = html_parser.parse(content).body?.text ?? content;

    // Return an auto-scrolling text
    return AutoScrollingText(
      text: displayText,
      style: FontPalette.medium13.copyWith(color: context.colorTheme.textRegular),
      scrollSpeed: 50.0,
      pauseDuration: const Duration(seconds: 2),
    );
  }
}

class NotificationPopup extends StatefulWidget {
  final List<HomeNotificationModel> notifications;
  const NotificationPopup({super.key, required this.notifications});

  @override
  State<NotificationPopup> createState() => _NotificationPopupState();
}

class _NotificationPopupState extends State<NotificationPopup> {
  int currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.notifications.length,
        (index) => Container(
          width: 8.0,
          height: 8.0,
          margin: EdgeInsets.symmetric(horizontal: 4.0.gw),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: currentIndex == index ? context.theme.primaryColor : Colors.white.withNewOpacity(0.5),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: SizedBox(
        width: 0.9.gsw,
        height: 0.45.gsh,
        child: Stack(
          children: [
            // Background PageView
            PageView.builder(
              controller: _pageController,
              itemCount: widget.notifications.length,
              onPageChanged: (index) => setState(() => currentIndex = index),
              itemBuilder: (context, index) {
                return Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(Assets.notificationBG),
                      fit: BoxFit.fill,
                    ),
                  ),
                );
              },
            ),

            // Content
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.gw),
                  child: Row(
                    children: [
                      Image.asset(
                        Assets.notificationTitleLogo,
                        width: 50.gw,
                        height: 50.gh,
                      ),
                      Text(
                        'latestActivities'.tr(),
                        style: FontPalette.semiBold14.copyWith(
                          color: context.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                16.verticalSpace,
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: widget.notifications.length,
                    onPageChanged: (index) => setState(() => currentIndex = index),
                    itemBuilder: (context, index) {
                      final notification = widget.notifications[index];
                      return SingleChildScrollView(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: GestureDetector(
                          onTap: () => AppNavigationHandler.handleNavigation(context,
                              jumpType: notification.jumpType, jumpUrl: notification.jumpUrl),
                          child: Column(
                            children: [
                              Text(
                                notification.title,
                                style: FontPalette.semiBold16,
                                textAlign: TextAlign.center,
                              ),
                              16.verticalSpace,
                              if (notification.imageUrl.isNotEmpty)
                                CachedNetworkImage(
                                  width: 0.70.gsw,
                                  imageUrl: notification.imageUrl,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => ShimmerWidget(
                                    width: double.infinity,
                                    color: Colors.transparent,
                                    height: 200.gh,
                                  ),
                                  errorWidget: (context, url, error) => Icon(
                                    Icons.broken_image_outlined,
                                    size: 48.gw,
                                    color: Colors.transparent,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                8.verticalSpace,
                _buildPageIndicator(),
                20.verticalSpace,
              ],
            ),
          ],
        ),
      ),
    );
  }
}
