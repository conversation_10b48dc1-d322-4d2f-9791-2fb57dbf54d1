import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ContractAssetSection extends StatelessWidget {
  final double? amount;

  const ContractAssetSection({super.key, required this.amount});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.gr),
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.primaryColor,
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.primaryColor.withNewOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        children: [
          Text(
            'totalAssets'.tr(),
            style: context.textTheme.secondary.fs20.opa80
          ),
          FlipText(
            amount ?? 0,
            isCurrency: true,
            showCurrencyDropdown: true,
            dropdownIconColor: Colors.white.withNewOpacity(0.8),
            style: context.textTheme.secondary.fs25.w600.ffAkz.copyWith(height: 1.2),
          ),
        ],
      ),
    );
  }
}
