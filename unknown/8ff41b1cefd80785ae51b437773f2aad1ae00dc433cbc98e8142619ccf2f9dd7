import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class LoadingFooter extends StatelessWidget {
  const LoadingFooter({super.key});

  ///logo
  Widget _logo() {
    return Container();
  }

  ///提示语
  ///
  /// [showText] 提示内容
  Widget _prompt(String showText, BuildContext context, {bool isShowIndicator = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isShowIndicator) ...[
          SizedBox(width: 15.gw, height: 15.gw, child: const CircularProgressIndicator()),
          10.horizontalSpace,
        ],
        Text(
          showText.tr(),
          style:
              FontPalette.bold12.copyWith(color: context.colorTheme.textRegular, fontFamily: 'Akzidenz-Grotesk'),
        ),
      ],
    );
  }

  ///提示语和log
  ///
  /// [showText] 提示内容
  Widget _hint(String showText, BuildContext context, {bool isShowIndicator = false}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.gh),
      child: Column(
        children: [_prompt(showText, context), SizedBox(height: 4.gh), _logo()],
      ),
    );
  }

  ///列表头
  CustomFooter _footer(BuildContext context) {
    Widget body = _hint("pullToLoadMore", context);
    return CustomFooter(
      builder: (context, mode) {
        if (mode == LoadStatus.idle) {
          body = _hint("pullToLoadMore", context);
        } else if (mode == LoadStatus.loading) {
          body = _hint("loading", context, isShowIndicator: true);
        } else if (mode == LoadStatus.failed) {
          body = _hint("failedToLoad", context);
        } else if (mode == LoadStatus.canLoading) {
          body = _hint("releaseToLoad", context);
        } else {
          body = _hint("noMoreData", context);
        }
        return body;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _footer(context);
  }
}
