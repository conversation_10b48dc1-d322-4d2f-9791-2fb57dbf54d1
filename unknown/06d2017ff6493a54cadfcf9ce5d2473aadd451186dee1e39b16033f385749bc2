import 'package:flutter/material.dart';

import 'package:gp_stock_app/shared/theme/font_pallette.dart';

import '../../../../utils/trading_utils.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockInfoItem extends StatelessWidget {
  final String label;
  final num? value;
  final int precision;
  final Color color;
  final bool isBold;
  final bool isPrice; // For K format
  final bool isVolume; // For B/M/K format
  final bool isPercent; // For % values
  final bool isLotSize; // For Lot format

  const StockInfoItem(
    this.label,
    this.value,
    this.color, {
    this.precision = 2,
    this.isBold = false,
    this.isPrice = false,
    this.isVolume = false,
    this.isPercent = false,
    this.isLotSize = false,
    super.key,
  });

  String getFormattedValue(BuildContext context) {
    if (isLotSize) {
      return TradingUtils.formatLotSize(value);
    } else if (isPrice) {
      return TradingUtils.formatPrice(value, precision: precision);
    } else if (isVolume) {
      return TradingUtils.formatVolume(value, context);
    } else if (isPercent) {
      return TradingUtils.formatPercentage(value);
    }
    return TradingUtils.formatNumber(value);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 14,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: SecFontPalette.normal11.copyWith(
            color: context.colorTheme.textRegular.withValues(alpha: 0.4),
          ),
        ),
        Text(
          getFormattedValue(context),
          style: isBold ? SecFontPalette.bold11.copyWith(color: color) : SecFontPalette.normal11.copyWith(color: color),
        ),
      ],
    );
  }
}
