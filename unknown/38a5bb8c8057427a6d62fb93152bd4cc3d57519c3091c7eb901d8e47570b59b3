import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/widgets/assets_card.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/account_order_list_view.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import '../../0_home/account_screen_cubit_v2.dart';

class AccountSpotDetailScreen extends StatelessWidget {
  final MarketCategoryState viewModel;

  const AccountSpotDetailScreen({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverToBoxAdapter(
            child: _buildAssetCard(context),
          ),
          SliverOverlapAbsorber(
            handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            sliver: SliverPersistentHeader(
              pinned: true,
              delegate: SliverAppBarDelegate(
                maxHeight: 55,
                minHeight: 55,
                child: CommonTabBar(
                  height: 55,
                  padding: EdgeInsets.only(left: 14.gw),
                  labelPadding: EdgeInsets.symmetric(horizontal: 10.gw),
                  tabAlignment: TabAlignment.start,
                  backgroundColor: context.theme.scaffoldBackgroundColor,
                  data: viewModel.details.entries.map((e) => "${tr(e.key.nameKey)}${e.value.countIfNotEmpty}").toList(),
                  currentIndex: viewModel.selectedIndex,
                  onTap: (index) => cubit.updateSpotMarketSubScreenIndex(index),
                ),
              ),
            ),
          ),
        ];
      },
      body: DirectSlideView(
        pages: viewModel.details.entries.map((entry) {
          final type = entry.key;
          final orderListState = entry.value;
          return AccountOrderListView(
            key: Key("${viewModel.category.nameKey}_${type.nameKey}"),
            marketCategory: viewModel.category,
            type: type,
            orderListState: orderListState,
            onFetch: (isLoadMore) {
              cubit.fetchMarketOrderList(
                category: viewModel.category,
                type: type,
                orderListState: orderListState,
                isLoadMore: isLoadMore,
              );
            },
          );
        }).toList(),
        pageIndex: viewModel.selectedIndex,
        onPageChanged: (index) => cubit.updateSpotMarketSubScreenIndex(index),
      ),
    );
  }

  Widget _buildAssetCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Assets Card
          BlocSelector<AccountInfoCubit, AccountInfoState, AccountInfoData?>(
            selector: (state) => state.accountInfo,
            builder: (context, model) {
              return AssetsCard(
                assetTitle: 'position_market_value'.tr(),
                totalAssets: viewModel.totalAssets,
                todayEarnings: viewModel.floatingPnl,
                availableBalance: model?.usableCash,
                myInterest: model?.interestCash,
                frozenAmount: model?.freezeCash,
                currency: model?.currency,
              );
            },
          ),
          14.verticalSpace,
          // Action Buttons
          buildActionButtons(context),
        ],
      ),
    );
  }

  Widget buildActionButtons(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 5,
      crossAxisSpacing: 0,
      children: [
        BuildActionButton(
          label: 'topUpDeposit'.tr(),
          icon: Assets.myAssetIcon,
          onTap: () {
            context.verifyAuth(
              () => Navigator.pushNamed(context, routeDepositMain),
            );
          },
        ),
        BuildActionButton(
          label: 'cashOut'.tr(),
          icon: Assets.withdrawIcon,
          onTap: () {
            context.verifyAuth(() => context.verifyRealName(() => Navigator.pushNamed(context, routeWithdrawMain)));
          },
        ),
        BuildActionButton(
          label: 'tradingCenter'.tr(),
          icon: Assets.tradingIcon,
          onTap: () {
            final category = viewModel.category;
            context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade);
            context.read<MarketCubit>().updateMainHeaderTab(1);
            if (category.code < 4) {
              /// 股票类型
              context.read<MarketCubit>().updateMainHeaderTab(0);
              context.read<MarketCubit>().updateTodaysTab(TodaysTab.fromCode(category.code));
            } else if (category == MarketCategory.stockIndex) {
              context.read<MarketCubit>().updateMainHeaderTab(1);
            } else if (category == MarketCategory.cnFutures) {
              context.read<MarketCubit>().updateMainHeaderTab(2);
            }

            Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
          },
        ),
        BuildActionButton(
          label: 'fundRecords'.tr(),
          icon: Assets.recordsIcon,
          onTap: () => Navigator.pushNamed(context, routeFundRecords),
        ),
        BuildActionButton(
          label: 'transactionHistory'.tr(),
          icon: Assets.historyIcon,
          onTap: () => Navigator.pushNamed(context, routeSpotAndContractHistory),
        ),
      ],
    );
  }
}
