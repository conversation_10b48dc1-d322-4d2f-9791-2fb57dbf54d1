// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderResponse _$OrderResponseFromJson(Map<String, dynamic> json) {
  return _OrderResponse.fromJson(json);
}

/// @nodoc
mixin _$OrderResponse {
  int? get code => throw _privateConstructorUsedError;
  OrderData? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;
  String? get sign => throw _privateConstructorUsedError;

  /// Serializes this OrderResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderResponseCopyWith<OrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderResponseCopyWith<$Res> {
  factory $OrderResponseCopyWith(
          OrderResponse value, $Res Function(OrderResponse) then) =
      _$OrderResponseCopyWithImpl<$Res, OrderResponse>;
  @useResult
  $Res call({int? code, OrderData? data, String? msg, String? sign});

  $OrderDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$OrderResponseCopyWithImpl<$Res, $Val extends OrderResponse>
    implements $OrderResponseCopyWith<$Res> {
  _$OrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as OrderData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $OrderDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderResponseImplCopyWith<$Res>
    implements $OrderResponseCopyWith<$Res> {
  factory _$$OrderResponseImplCopyWith(
          _$OrderResponseImpl value, $Res Function(_$OrderResponseImpl) then) =
      __$$OrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, OrderData? data, String? msg, String? sign});

  @override
  $OrderDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$OrderResponseImplCopyWithImpl<$Res>
    extends _$OrderResponseCopyWithImpl<$Res, _$OrderResponseImpl>
    implements _$$OrderResponseImplCopyWith<$Res> {
  __$$OrderResponseImplCopyWithImpl(
      _$OrderResponseImpl _value, $Res Function(_$OrderResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_$OrderResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as OrderData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderResponseImpl implements _OrderResponse {
  const _$OrderResponseImpl({this.code, this.data, this.msg, this.sign});

  factory _$OrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderResponseImplFromJson(json);

  @override
  final int? code;
  @override
  final OrderData? data;
  @override
  final String? msg;
  @override
  final String? sign;

  @override
  String toString() {
    return 'OrderResponse(code: $code, data: $data, msg: $msg, sign: $sign)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg) &&
            (identical(other.sign, sign) || other.sign == sign));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg, sign);

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderResponseImplCopyWith<_$OrderResponseImpl> get copyWith =>
      __$$OrderResponseImplCopyWithImpl<_$OrderResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderResponseImplToJson(
      this,
    );
  }
}

abstract class _OrderResponse implements OrderResponse {
  const factory _OrderResponse(
      {final int? code,
      final OrderData? data,
      final String? msg,
      final String? sign}) = _$OrderResponseImpl;

  factory _OrderResponse.fromJson(Map<String, dynamic> json) =
      _$OrderResponseImpl.fromJson;

  @override
  int? get code;
  @override
  OrderData? get data;
  @override
  String? get msg;
  @override
  String? get sign;

  /// Create a copy of OrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderResponseImplCopyWith<_$OrderResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderData _$OrderDataFromJson(Map<String, dynamic> json) {
  return _OrderData.fromJson(json);
}

/// @nodoc
mixin _$OrderData {
  int? get current => throw _privateConstructorUsedError;
  bool? get hasNext => throw _privateConstructorUsedError;
  List<OrderRecord>? get records => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this OrderData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderDataCopyWith<OrderData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderDataCopyWith<$Res> {
  factory $OrderDataCopyWith(OrderData value, $Res Function(OrderData) then) =
      _$OrderDataCopyWithImpl<$Res, OrderData>;
  @useResult
  $Res call(
      {int? current, bool? hasNext, List<OrderRecord>? records, int? total});
}

/// @nodoc
class _$OrderDataCopyWithImpl<$Res, $Val extends OrderData>
    implements $OrderDataCopyWith<$Res> {
  _$OrderDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<OrderRecord>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderDataImplCopyWith<$Res>
    implements $OrderDataCopyWith<$Res> {
  factory _$$OrderDataImplCopyWith(
          _$OrderDataImpl value, $Res Function(_$OrderDataImpl) then) =
      __$$OrderDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? current, bool? hasNext, List<OrderRecord>? records, int? total});
}

/// @nodoc
class __$$OrderDataImplCopyWithImpl<$Res>
    extends _$OrderDataCopyWithImpl<$Res, _$OrderDataImpl>
    implements _$$OrderDataImplCopyWith<$Res> {
  __$$OrderDataImplCopyWithImpl(
      _$OrderDataImpl _value, $Res Function(_$OrderDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_$OrderDataImpl(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<OrderRecord>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderDataImpl implements _OrderData {
  const _$OrderDataImpl(
      {this.current,
      this.hasNext,
      final List<OrderRecord>? records,
      this.total})
      : _records = records;

  factory _$OrderDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderDataImplFromJson(json);

  @override
  final int? current;
  @override
  final bool? hasNext;
  final List<OrderRecord>? _records;
  @override
  List<OrderRecord>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? total;

  @override
  String toString() {
    return 'OrderData(current: $current, hasNext: $hasNext, records: $records, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderDataImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, hasNext,
      const DeepCollectionEquality().hash(_records), total);

  /// Create a copy of OrderData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderDataImplCopyWith<_$OrderDataImpl> get copyWith =>
      __$$OrderDataImplCopyWithImpl<_$OrderDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderDataImplToJson(
      this,
    );
  }
}

abstract class _OrderData implements OrderData {
  const factory _OrderData(
      {final int? current,
      final bool? hasNext,
      final List<OrderRecord>? records,
      final int? total}) = _$OrderDataImpl;

  factory _OrderData.fromJson(Map<String, dynamic> json) =
      _$OrderDataImpl.fromJson;

  @override
  int? get current;
  @override
  bool? get hasNext;
  @override
  List<OrderRecord>? get records;
  @override
  int? get total;

  /// Create a copy of OrderData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderDataImplCopyWith<_$OrderDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderRecord _$OrderRecordFromJson(Map<String, dynamic> json) {
  return _OrderRecord.fromJson(json);
}

/// @nodoc
mixin _$OrderRecord {
  String? get cancelTime => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  double? get dealNum => throw _privateConstructorUsedError;
  double? get dealPrice => throw _privateConstructorUsedError;
  String? get dealTime => throw _privateConstructorUsedError;
  int? get direction => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get market => throw _privateConstructorUsedError;
  int? get priceType => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get symbol => throw _privateConstructorUsedError;
  String? get symbolName => throw _privateConstructorUsedError;
  double? get tradeNum => throw _privateConstructorUsedError;
  double? get tradePrice => throw _privateConstructorUsedError;
  String? get tradeTime => throw _privateConstructorUsedError;
  int? get tradeType => throw _privateConstructorUsedError;
  double? get transactionAmount => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  double? get availableMargin => throw _privateConstructorUsedError;
  double? get marginAmount => throw _privateConstructorUsedError;
  double? get buyAvgPrice => throw _privateConstructorUsedError;
  double? get buyTotalNum => throw _privateConstructorUsedError;
  double? get costPrice => throw _privateConstructorUsedError;
  double? get disableNum => throw _privateConstructorUsedError;
  double? get floatingProfitLoss => throw _privateConstructorUsedError;
  double? get floatingProfitLossRate => throw _privateConstructorUsedError;
  double? get marketValue => throw _privateConstructorUsedError;
  double? get restNum => throw _privateConstructorUsedError;
  double? get takeProfitValue => throw _privateConstructorUsedError;
  double? get stopLossValue => throw _privateConstructorUsedError;
  double? get appendMargin => throw _privateConstructorUsedError;
  double? get marginRatio => throw _privateConstructorUsedError;
  double? get warningLine => throw _privateConstructorUsedError;
  double? get distanceWarningLine => throw _privateConstructorUsedError;
  double? get closeLine => throw _privateConstructorUsedError;
  double? get distanceCloseLine => throw _privateConstructorUsedError;
  double? get feeAmount => throw _privateConstructorUsedError;
  int? get positionDays => throw _privateConstructorUsedError;
  double? get positionTotalNum => throw _privateConstructorUsedError;
  String? get securityType => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  double? get stockPrice => throw _privateConstructorUsedError;
  int? get contractAccountId => throw _privateConstructorUsedError;
  int? get periodType => throw _privateConstructorUsedError;
  int? get multiple => throw _privateConstructorUsedError;
  double? get winAmount => throw _privateConstructorUsedError;
  double? get tradeFee => throw _privateConstructorUsedError;
  int? get contractId => throw _privateConstructorUsedError;
  int? get contractType => throw _privateConstructorUsedError;
  List<ChargeDetail>? get chargeDetailList =>
      throw _privateConstructorUsedError;

  /// Serializes this OrderRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderRecordCopyWith<OrderRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderRecordCopyWith<$Res> {
  factory $OrderRecordCopyWith(
          OrderRecord value, $Res Function(OrderRecord) then) =
      _$OrderRecordCopyWithImpl<$Res, OrderRecord>;
  @useResult
  $Res call(
      {String? cancelTime,
      String? currency,
      double? dealNum,
      double? dealPrice,
      String? dealTime,
      int? direction,
      int? id,
      String? market,
      int? priceType,
      int? status,
      String? symbol,
      String? symbolName,
      double? tradeNum,
      double? tradePrice,
      String? tradeTime,
      int? tradeType,
      double? transactionAmount,
      int? type,
      double? availableMargin,
      double? marginAmount,
      double? buyAvgPrice,
      double? buyTotalNum,
      double? costPrice,
      double? disableNum,
      double? floatingProfitLoss,
      double? floatingProfitLossRate,
      double? marketValue,
      double? restNum,
      double? takeProfitValue,
      double? stopLossValue,
      double? appendMargin,
      double? marginRatio,
      double? warningLine,
      double? distanceWarningLine,
      double? closeLine,
      double? distanceCloseLine,
      double? feeAmount,
      int? positionDays,
      double? positionTotalNum,
      String? securityType,
      String? createTime,
      double? stockPrice,
      int? contractAccountId,
      int? periodType,
      int? multiple,
      double? winAmount,
      double? tradeFee,
      int? contractId,
      int? contractType,
      List<ChargeDetail>? chargeDetailList});
}

/// @nodoc
class _$OrderRecordCopyWithImpl<$Res, $Val extends OrderRecord>
    implements $OrderRecordCopyWith<$Res> {
  _$OrderRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cancelTime = freezed,
    Object? currency = freezed,
    Object? dealNum = freezed,
    Object? dealPrice = freezed,
    Object? dealTime = freezed,
    Object? direction = freezed,
    Object? id = freezed,
    Object? market = freezed,
    Object? priceType = freezed,
    Object? status = freezed,
    Object? symbol = freezed,
    Object? symbolName = freezed,
    Object? tradeNum = freezed,
    Object? tradePrice = freezed,
    Object? tradeTime = freezed,
    Object? tradeType = freezed,
    Object? transactionAmount = freezed,
    Object? type = freezed,
    Object? availableMargin = freezed,
    Object? marginAmount = freezed,
    Object? buyAvgPrice = freezed,
    Object? buyTotalNum = freezed,
    Object? costPrice = freezed,
    Object? disableNum = freezed,
    Object? floatingProfitLoss = freezed,
    Object? floatingProfitLossRate = freezed,
    Object? marketValue = freezed,
    Object? restNum = freezed,
    Object? takeProfitValue = freezed,
    Object? stopLossValue = freezed,
    Object? appendMargin = freezed,
    Object? marginRatio = freezed,
    Object? warningLine = freezed,
    Object? distanceWarningLine = freezed,
    Object? closeLine = freezed,
    Object? distanceCloseLine = freezed,
    Object? feeAmount = freezed,
    Object? positionDays = freezed,
    Object? positionTotalNum = freezed,
    Object? securityType = freezed,
    Object? createTime = freezed,
    Object? stockPrice = freezed,
    Object? contractAccountId = freezed,
    Object? periodType = freezed,
    Object? multiple = freezed,
    Object? winAmount = freezed,
    Object? tradeFee = freezed,
    Object? contractId = freezed,
    Object? contractType = freezed,
    Object? chargeDetailList = freezed,
  }) {
    return _then(_value.copyWith(
      cancelTime: freezed == cancelTime
          ? _value.cancelTime
          : cancelTime // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      dealNum: freezed == dealNum
          ? _value.dealNum
          : dealNum // ignore: cast_nullable_to_non_nullable
              as double?,
      dealPrice: freezed == dealPrice
          ? _value.dealPrice
          : dealPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      dealTime: freezed == dealTime
          ? _value.dealTime
          : dealTime // ignore: cast_nullable_to_non_nullable
              as String?,
      direction: freezed == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      priceType: freezed == priceType
          ? _value.priceType
          : priceType // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      symbolName: freezed == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeNum: freezed == tradeNum
          ? _value.tradeNum
          : tradeNum // ignore: cast_nullable_to_non_nullable
              as double?,
      tradePrice: freezed == tradePrice
          ? _value.tradePrice
          : tradePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      tradeTime: freezed == tradeTime
          ? _value.tradeTime
          : tradeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeType: freezed == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int?,
      transactionAmount: freezed == transactionAmount
          ? _value.transactionAmount
          : transactionAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      availableMargin: freezed == availableMargin
          ? _value.availableMargin
          : availableMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      marginAmount: freezed == marginAmount
          ? _value.marginAmount
          : marginAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      buyAvgPrice: freezed == buyAvgPrice
          ? _value.buyAvgPrice
          : buyAvgPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      buyTotalNum: freezed == buyTotalNum
          ? _value.buyTotalNum
          : buyTotalNum // ignore: cast_nullable_to_non_nullable
              as double?,
      costPrice: freezed == costPrice
          ? _value.costPrice
          : costPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      disableNum: freezed == disableNum
          ? _value.disableNum
          : disableNum // ignore: cast_nullable_to_non_nullable
              as double?,
      floatingProfitLoss: freezed == floatingProfitLoss
          ? _value.floatingProfitLoss
          : floatingProfitLoss // ignore: cast_nullable_to_non_nullable
              as double?,
      floatingProfitLossRate: freezed == floatingProfitLossRate
          ? _value.floatingProfitLossRate
          : floatingProfitLossRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketValue: freezed == marketValue
          ? _value.marketValue
          : marketValue // ignore: cast_nullable_to_non_nullable
              as double?,
      restNum: freezed == restNum
          ? _value.restNum
          : restNum // ignore: cast_nullable_to_non_nullable
              as double?,
      takeProfitValue: freezed == takeProfitValue
          ? _value.takeProfitValue
          : takeProfitValue // ignore: cast_nullable_to_non_nullable
              as double?,
      stopLossValue: freezed == stopLossValue
          ? _value.stopLossValue
          : stopLossValue // ignore: cast_nullable_to_non_nullable
              as double?,
      appendMargin: freezed == appendMargin
          ? _value.appendMargin
          : appendMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      marginRatio: freezed == marginRatio
          ? _value.marginRatio
          : marginRatio // ignore: cast_nullable_to_non_nullable
              as double?,
      warningLine: freezed == warningLine
          ? _value.warningLine
          : warningLine // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceWarningLine: freezed == distanceWarningLine
          ? _value.distanceWarningLine
          : distanceWarningLine // ignore: cast_nullable_to_non_nullable
              as double?,
      closeLine: freezed == closeLine
          ? _value.closeLine
          : closeLine // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceCloseLine: freezed == distanceCloseLine
          ? _value.distanceCloseLine
          : distanceCloseLine // ignore: cast_nullable_to_non_nullable
              as double?,
      feeAmount: freezed == feeAmount
          ? _value.feeAmount
          : feeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      positionDays: freezed == positionDays
          ? _value.positionDays
          : positionDays // ignore: cast_nullable_to_non_nullable
              as int?,
      positionTotalNum: freezed == positionTotalNum
          ? _value.positionTotalNum
          : positionTotalNum // ignore: cast_nullable_to_non_nullable
              as double?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      stockPrice: freezed == stockPrice
          ? _value.stockPrice
          : stockPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      contractAccountId: freezed == contractAccountId
          ? _value.contractAccountId
          : contractAccountId // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      winAmount: freezed == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      tradeFee: freezed == tradeFee
          ? _value.tradeFee
          : tradeFee // ignore: cast_nullable_to_non_nullable
              as double?,
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as int?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as int?,
      chargeDetailList: freezed == chargeDetailList
          ? _value.chargeDetailList
          : chargeDetailList // ignore: cast_nullable_to_non_nullable
              as List<ChargeDetail>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderRecordImplCopyWith<$Res>
    implements $OrderRecordCopyWith<$Res> {
  factory _$$OrderRecordImplCopyWith(
          _$OrderRecordImpl value, $Res Function(_$OrderRecordImpl) then) =
      __$$OrderRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? cancelTime,
      String? currency,
      double? dealNum,
      double? dealPrice,
      String? dealTime,
      int? direction,
      int? id,
      String? market,
      int? priceType,
      int? status,
      String? symbol,
      String? symbolName,
      double? tradeNum,
      double? tradePrice,
      String? tradeTime,
      int? tradeType,
      double? transactionAmount,
      int? type,
      double? availableMargin,
      double? marginAmount,
      double? buyAvgPrice,
      double? buyTotalNum,
      double? costPrice,
      double? disableNum,
      double? floatingProfitLoss,
      double? floatingProfitLossRate,
      double? marketValue,
      double? restNum,
      double? takeProfitValue,
      double? stopLossValue,
      double? appendMargin,
      double? marginRatio,
      double? warningLine,
      double? distanceWarningLine,
      double? closeLine,
      double? distanceCloseLine,
      double? feeAmount,
      int? positionDays,
      double? positionTotalNum,
      String? securityType,
      String? createTime,
      double? stockPrice,
      int? contractAccountId,
      int? periodType,
      int? multiple,
      double? winAmount,
      double? tradeFee,
      int? contractId,
      int? contractType,
      List<ChargeDetail>? chargeDetailList});
}

/// @nodoc
class __$$OrderRecordImplCopyWithImpl<$Res>
    extends _$OrderRecordCopyWithImpl<$Res, _$OrderRecordImpl>
    implements _$$OrderRecordImplCopyWith<$Res> {
  __$$OrderRecordImplCopyWithImpl(
      _$OrderRecordImpl _value, $Res Function(_$OrderRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cancelTime = freezed,
    Object? currency = freezed,
    Object? dealNum = freezed,
    Object? dealPrice = freezed,
    Object? dealTime = freezed,
    Object? direction = freezed,
    Object? id = freezed,
    Object? market = freezed,
    Object? priceType = freezed,
    Object? status = freezed,
    Object? symbol = freezed,
    Object? symbolName = freezed,
    Object? tradeNum = freezed,
    Object? tradePrice = freezed,
    Object? tradeTime = freezed,
    Object? tradeType = freezed,
    Object? transactionAmount = freezed,
    Object? type = freezed,
    Object? availableMargin = freezed,
    Object? marginAmount = freezed,
    Object? buyAvgPrice = freezed,
    Object? buyTotalNum = freezed,
    Object? costPrice = freezed,
    Object? disableNum = freezed,
    Object? floatingProfitLoss = freezed,
    Object? floatingProfitLossRate = freezed,
    Object? marketValue = freezed,
    Object? restNum = freezed,
    Object? takeProfitValue = freezed,
    Object? stopLossValue = freezed,
    Object? appendMargin = freezed,
    Object? marginRatio = freezed,
    Object? warningLine = freezed,
    Object? distanceWarningLine = freezed,
    Object? closeLine = freezed,
    Object? distanceCloseLine = freezed,
    Object? feeAmount = freezed,
    Object? positionDays = freezed,
    Object? positionTotalNum = freezed,
    Object? securityType = freezed,
    Object? createTime = freezed,
    Object? stockPrice = freezed,
    Object? contractAccountId = freezed,
    Object? periodType = freezed,
    Object? multiple = freezed,
    Object? winAmount = freezed,
    Object? tradeFee = freezed,
    Object? contractId = freezed,
    Object? contractType = freezed,
    Object? chargeDetailList = freezed,
  }) {
    return _then(_$OrderRecordImpl(
      cancelTime: freezed == cancelTime
          ? _value.cancelTime
          : cancelTime // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      dealNum: freezed == dealNum
          ? _value.dealNum
          : dealNum // ignore: cast_nullable_to_non_nullable
              as double?,
      dealPrice: freezed == dealPrice
          ? _value.dealPrice
          : dealPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      dealTime: freezed == dealTime
          ? _value.dealTime
          : dealTime // ignore: cast_nullable_to_non_nullable
              as String?,
      direction: freezed == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      priceType: freezed == priceType
          ? _value.priceType
          : priceType // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      symbolName: freezed == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeNum: freezed == tradeNum
          ? _value.tradeNum
          : tradeNum // ignore: cast_nullable_to_non_nullable
              as double?,
      tradePrice: freezed == tradePrice
          ? _value.tradePrice
          : tradePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      tradeTime: freezed == tradeTime
          ? _value.tradeTime
          : tradeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeType: freezed == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int?,
      transactionAmount: freezed == transactionAmount
          ? _value.transactionAmount
          : transactionAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      availableMargin: freezed == availableMargin
          ? _value.availableMargin
          : availableMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      marginAmount: freezed == marginAmount
          ? _value.marginAmount
          : marginAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      buyAvgPrice: freezed == buyAvgPrice
          ? _value.buyAvgPrice
          : buyAvgPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      buyTotalNum: freezed == buyTotalNum
          ? _value.buyTotalNum
          : buyTotalNum // ignore: cast_nullable_to_non_nullable
              as double?,
      costPrice: freezed == costPrice
          ? _value.costPrice
          : costPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      disableNum: freezed == disableNum
          ? _value.disableNum
          : disableNum // ignore: cast_nullable_to_non_nullable
              as double?,
      floatingProfitLoss: freezed == floatingProfitLoss
          ? _value.floatingProfitLoss
          : floatingProfitLoss // ignore: cast_nullable_to_non_nullable
              as double?,
      floatingProfitLossRate: freezed == floatingProfitLossRate
          ? _value.floatingProfitLossRate
          : floatingProfitLossRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketValue: freezed == marketValue
          ? _value.marketValue
          : marketValue // ignore: cast_nullable_to_non_nullable
              as double?,
      restNum: freezed == restNum
          ? _value.restNum
          : restNum // ignore: cast_nullable_to_non_nullable
              as double?,
      takeProfitValue: freezed == takeProfitValue
          ? _value.takeProfitValue
          : takeProfitValue // ignore: cast_nullable_to_non_nullable
              as double?,
      stopLossValue: freezed == stopLossValue
          ? _value.stopLossValue
          : stopLossValue // ignore: cast_nullable_to_non_nullable
              as double?,
      appendMargin: freezed == appendMargin
          ? _value.appendMargin
          : appendMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      marginRatio: freezed == marginRatio
          ? _value.marginRatio
          : marginRatio // ignore: cast_nullable_to_non_nullable
              as double?,
      warningLine: freezed == warningLine
          ? _value.warningLine
          : warningLine // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceWarningLine: freezed == distanceWarningLine
          ? _value.distanceWarningLine
          : distanceWarningLine // ignore: cast_nullable_to_non_nullable
              as double?,
      closeLine: freezed == closeLine
          ? _value.closeLine
          : closeLine // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceCloseLine: freezed == distanceCloseLine
          ? _value.distanceCloseLine
          : distanceCloseLine // ignore: cast_nullable_to_non_nullable
              as double?,
      feeAmount: freezed == feeAmount
          ? _value.feeAmount
          : feeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      positionDays: freezed == positionDays
          ? _value.positionDays
          : positionDays // ignore: cast_nullable_to_non_nullable
              as int?,
      positionTotalNum: freezed == positionTotalNum
          ? _value.positionTotalNum
          : positionTotalNum // ignore: cast_nullable_to_non_nullable
              as double?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      stockPrice: freezed == stockPrice
          ? _value.stockPrice
          : stockPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      contractAccountId: freezed == contractAccountId
          ? _value.contractAccountId
          : contractAccountId // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      winAmount: freezed == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      tradeFee: freezed == tradeFee
          ? _value.tradeFee
          : tradeFee // ignore: cast_nullable_to_non_nullable
              as double?,
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as int?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as int?,
      chargeDetailList: freezed == chargeDetailList
          ? _value._chargeDetailList
          : chargeDetailList // ignore: cast_nullable_to_non_nullable
              as List<ChargeDetail>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderRecordImpl extends _OrderRecord {
  const _$OrderRecordImpl(
      {this.cancelTime,
      this.currency,
      this.dealNum,
      this.dealPrice,
      this.dealTime,
      this.direction,
      this.id,
      this.market,
      this.priceType,
      this.status,
      this.symbol,
      this.symbolName,
      this.tradeNum,
      this.tradePrice,
      this.tradeTime,
      this.tradeType,
      this.transactionAmount,
      this.type,
      this.availableMargin,
      this.marginAmount,
      this.buyAvgPrice,
      this.buyTotalNum,
      this.costPrice,
      this.disableNum,
      this.floatingProfitLoss,
      this.floatingProfitLossRate,
      this.marketValue,
      this.restNum,
      this.takeProfitValue,
      this.stopLossValue,
      this.appendMargin,
      this.marginRatio,
      this.warningLine,
      this.distanceWarningLine,
      this.closeLine,
      this.distanceCloseLine,
      this.feeAmount,
      this.positionDays,
      this.positionTotalNum,
      this.securityType,
      this.createTime,
      this.stockPrice,
      this.contractAccountId,
      this.periodType,
      this.multiple,
      this.winAmount,
      this.tradeFee,
      this.contractId,
      this.contractType,
      final List<ChargeDetail>? chargeDetailList})
      : _chargeDetailList = chargeDetailList,
        super._();

  factory _$OrderRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderRecordImplFromJson(json);

  @override
  final String? cancelTime;
  @override
  final String? currency;
  @override
  final double? dealNum;
  @override
  final double? dealPrice;
  @override
  final String? dealTime;
  @override
  final int? direction;
  @override
  final int? id;
  @override
  final String? market;
  @override
  final int? priceType;
  @override
  final int? status;
  @override
  final String? symbol;
  @override
  final String? symbolName;
  @override
  final double? tradeNum;
  @override
  final double? tradePrice;
  @override
  final String? tradeTime;
  @override
  final int? tradeType;
  @override
  final double? transactionAmount;
  @override
  final int? type;
  @override
  final double? availableMargin;
  @override
  final double? marginAmount;
  @override
  final double? buyAvgPrice;
  @override
  final double? buyTotalNum;
  @override
  final double? costPrice;
  @override
  final double? disableNum;
  @override
  final double? floatingProfitLoss;
  @override
  final double? floatingProfitLossRate;
  @override
  final double? marketValue;
  @override
  final double? restNum;
  @override
  final double? takeProfitValue;
  @override
  final double? stopLossValue;
  @override
  final double? appendMargin;
  @override
  final double? marginRatio;
  @override
  final double? warningLine;
  @override
  final double? distanceWarningLine;
  @override
  final double? closeLine;
  @override
  final double? distanceCloseLine;
  @override
  final double? feeAmount;
  @override
  final int? positionDays;
  @override
  final double? positionTotalNum;
  @override
  final String? securityType;
  @override
  final String? createTime;
  @override
  final double? stockPrice;
  @override
  final int? contractAccountId;
  @override
  final int? periodType;
  @override
  final int? multiple;
  @override
  final double? winAmount;
  @override
  final double? tradeFee;
  @override
  final int? contractId;
  @override
  final int? contractType;
  final List<ChargeDetail>? _chargeDetailList;
  @override
  List<ChargeDetail>? get chargeDetailList {
    final value = _chargeDetailList;
    if (value == null) return null;
    if (_chargeDetailList is EqualUnmodifiableListView)
      return _chargeDetailList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'OrderRecord(cancelTime: $cancelTime, currency: $currency, dealNum: $dealNum, dealPrice: $dealPrice, dealTime: $dealTime, direction: $direction, id: $id, market: $market, priceType: $priceType, status: $status, symbol: $symbol, symbolName: $symbolName, tradeNum: $tradeNum, tradePrice: $tradePrice, tradeTime: $tradeTime, tradeType: $tradeType, transactionAmount: $transactionAmount, type: $type, availableMargin: $availableMargin, marginAmount: $marginAmount, buyAvgPrice: $buyAvgPrice, buyTotalNum: $buyTotalNum, costPrice: $costPrice, disableNum: $disableNum, floatingProfitLoss: $floatingProfitLoss, floatingProfitLossRate: $floatingProfitLossRate, marketValue: $marketValue, restNum: $restNum, takeProfitValue: $takeProfitValue, stopLossValue: $stopLossValue, appendMargin: $appendMargin, marginRatio: $marginRatio, warningLine: $warningLine, distanceWarningLine: $distanceWarningLine, closeLine: $closeLine, distanceCloseLine: $distanceCloseLine, feeAmount: $feeAmount, positionDays: $positionDays, positionTotalNum: $positionTotalNum, securityType: $securityType, createTime: $createTime, stockPrice: $stockPrice, contractAccountId: $contractAccountId, periodType: $periodType, multiple: $multiple, winAmount: $winAmount, tradeFee: $tradeFee, contractId: $contractId, contractType: $contractType, chargeDetailList: $chargeDetailList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderRecordImpl &&
            (identical(other.cancelTime, cancelTime) ||
                other.cancelTime == cancelTime) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.dealNum, dealNum) || other.dealNum == dealNum) &&
            (identical(other.dealPrice, dealPrice) ||
                other.dealPrice == dealPrice) &&
            (identical(other.dealTime, dealTime) ||
                other.dealTime == dealTime) &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.priceType, priceType) ||
                other.priceType == priceType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.symbolName, symbolName) ||
                other.symbolName == symbolName) &&
            (identical(other.tradeNum, tradeNum) ||
                other.tradeNum == tradeNum) &&
            (identical(other.tradePrice, tradePrice) ||
                other.tradePrice == tradePrice) &&
            (identical(other.tradeTime, tradeTime) ||
                other.tradeTime == tradeTime) &&
            (identical(other.tradeType, tradeType) ||
                other.tradeType == tradeType) &&
            (identical(other.transactionAmount, transactionAmount) ||
                other.transactionAmount == transactionAmount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.availableMargin, availableMargin) ||
                other.availableMargin == availableMargin) &&
            (identical(other.marginAmount, marginAmount) ||
                other.marginAmount == marginAmount) &&
            (identical(other.buyAvgPrice, buyAvgPrice) ||
                other.buyAvgPrice == buyAvgPrice) &&
            (identical(other.buyTotalNum, buyTotalNum) ||
                other.buyTotalNum == buyTotalNum) &&
            (identical(other.costPrice, costPrice) ||
                other.costPrice == costPrice) &&
            (identical(other.disableNum, disableNum) ||
                other.disableNum == disableNum) &&
            (identical(other.floatingProfitLoss, floatingProfitLoss) ||
                other.floatingProfitLoss == floatingProfitLoss) &&
            (identical(other.floatingProfitLossRate, floatingProfitLossRate) ||
                other.floatingProfitLossRate == floatingProfitLossRate) &&
            (identical(other.marketValue, marketValue) ||
                other.marketValue == marketValue) &&
            (identical(other.restNum, restNum) || other.restNum == restNum) &&
            (identical(other.takeProfitValue, takeProfitValue) ||
                other.takeProfitValue == takeProfitValue) &&
            (identical(other.stopLossValue, stopLossValue) ||
                other.stopLossValue == stopLossValue) &&
            (identical(other.appendMargin, appendMargin) ||
                other.appendMargin == appendMargin) &&
            (identical(other.marginRatio, marginRatio) ||
                other.marginRatio == marginRatio) &&
            (identical(other.warningLine, warningLine) ||
                other.warningLine == warningLine) &&
            (identical(other.distanceWarningLine, distanceWarningLine) ||
                other.distanceWarningLine == distanceWarningLine) &&
            (identical(other.closeLine, closeLine) ||
                other.closeLine == closeLine) &&
            (identical(other.distanceCloseLine, distanceCloseLine) ||
                other.distanceCloseLine == distanceCloseLine) &&
            (identical(other.feeAmount, feeAmount) ||
                other.feeAmount == feeAmount) &&
            (identical(other.positionDays, positionDays) ||
                other.positionDays == positionDays) &&
            (identical(other.positionTotalNum, positionTotalNum) ||
                other.positionTotalNum == positionTotalNum) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.stockPrice, stockPrice) ||
                other.stockPrice == stockPrice) &&
            (identical(other.contractAccountId, contractAccountId) ||
                other.contractAccountId == contractAccountId) &&
            (identical(other.periodType, periodType) ||
                other.periodType == periodType) &&
            (identical(other.multiple, multiple) ||
                other.multiple == multiple) &&
            (identical(other.winAmount, winAmount) ||
                other.winAmount == winAmount) &&
            (identical(other.tradeFee, tradeFee) ||
                other.tradeFee == tradeFee) &&
            (identical(other.contractId, contractId) ||
                other.contractId == contractId) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            const DeepCollectionEquality()
                .equals(other._chargeDetailList, _chargeDetailList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        cancelTime,
        currency,
        dealNum,
        dealPrice,
        dealTime,
        direction,
        id,
        market,
        priceType,
        status,
        symbol,
        symbolName,
        tradeNum,
        tradePrice,
        tradeTime,
        tradeType,
        transactionAmount,
        type,
        availableMargin,
        marginAmount,
        buyAvgPrice,
        buyTotalNum,
        costPrice,
        disableNum,
        floatingProfitLoss,
        floatingProfitLossRate,
        marketValue,
        restNum,
        takeProfitValue,
        stopLossValue,
        appendMargin,
        marginRatio,
        warningLine,
        distanceWarningLine,
        closeLine,
        distanceCloseLine,
        feeAmount,
        positionDays,
        positionTotalNum,
        securityType,
        createTime,
        stockPrice,
        contractAccountId,
        periodType,
        multiple,
        winAmount,
        tradeFee,
        contractId,
        contractType,
        const DeepCollectionEquality().hash(_chargeDetailList)
      ]);

  /// Create a copy of OrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderRecordImplCopyWith<_$OrderRecordImpl> get copyWith =>
      __$$OrderRecordImplCopyWithImpl<_$OrderRecordImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderRecordImplToJson(
      this,
    );
  }
}

abstract class _OrderRecord extends OrderRecord {
  const factory _OrderRecord(
      {final String? cancelTime,
      final String? currency,
      final double? dealNum,
      final double? dealPrice,
      final String? dealTime,
      final int? direction,
      final int? id,
      final String? market,
      final int? priceType,
      final int? status,
      final String? symbol,
      final String? symbolName,
      final double? tradeNum,
      final double? tradePrice,
      final String? tradeTime,
      final int? tradeType,
      final double? transactionAmount,
      final int? type,
      final double? availableMargin,
      final double? marginAmount,
      final double? buyAvgPrice,
      final double? buyTotalNum,
      final double? costPrice,
      final double? disableNum,
      final double? floatingProfitLoss,
      final double? floatingProfitLossRate,
      final double? marketValue,
      final double? restNum,
      final double? takeProfitValue,
      final double? stopLossValue,
      final double? appendMargin,
      final double? marginRatio,
      final double? warningLine,
      final double? distanceWarningLine,
      final double? closeLine,
      final double? distanceCloseLine,
      final double? feeAmount,
      final int? positionDays,
      final double? positionTotalNum,
      final String? securityType,
      final String? createTime,
      final double? stockPrice,
      final int? contractAccountId,
      final int? periodType,
      final int? multiple,
      final double? winAmount,
      final double? tradeFee,
      final int? contractId,
      final int? contractType,
      final List<ChargeDetail>? chargeDetailList}) = _$OrderRecordImpl;
  const _OrderRecord._() : super._();

  factory _OrderRecord.fromJson(Map<String, dynamic> json) =
      _$OrderRecordImpl.fromJson;

  @override
  String? get cancelTime;
  @override
  String? get currency;
  @override
  double? get dealNum;
  @override
  double? get dealPrice;
  @override
  String? get dealTime;
  @override
  int? get direction;
  @override
  int? get id;
  @override
  String? get market;
  @override
  int? get priceType;
  @override
  int? get status;
  @override
  String? get symbol;
  @override
  String? get symbolName;
  @override
  double? get tradeNum;
  @override
  double? get tradePrice;
  @override
  String? get tradeTime;
  @override
  int? get tradeType;
  @override
  double? get transactionAmount;
  @override
  int? get type;
  @override
  double? get availableMargin;
  @override
  double? get marginAmount;
  @override
  double? get buyAvgPrice;
  @override
  double? get buyTotalNum;
  @override
  double? get costPrice;
  @override
  double? get disableNum;
  @override
  double? get floatingProfitLoss;
  @override
  double? get floatingProfitLossRate;
  @override
  double? get marketValue;
  @override
  double? get restNum;
  @override
  double? get takeProfitValue;
  @override
  double? get stopLossValue;
  @override
  double? get appendMargin;
  @override
  double? get marginRatio;
  @override
  double? get warningLine;
  @override
  double? get distanceWarningLine;
  @override
  double? get closeLine;
  @override
  double? get distanceCloseLine;
  @override
  double? get feeAmount;
  @override
  int? get positionDays;
  @override
  double? get positionTotalNum;
  @override
  String? get securityType;
  @override
  String? get createTime;
  @override
  double? get stockPrice;
  @override
  int? get contractAccountId;
  @override
  int? get periodType;
  @override
  int? get multiple;
  @override
  double? get winAmount;
  @override
  double? get tradeFee;
  @override
  int? get contractId;
  @override
  int? get contractType;
  @override
  List<ChargeDetail>? get chargeDetailList;

  /// Create a copy of OrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderRecordImplCopyWith<_$OrderRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChargeDetail _$ChargeDetailFromJson(Map<String, dynamic> json) {
  return _ChargeDetail.fromJson(json);
}

/// @nodoc
mixin _$ChargeDetail {
  double get fee => throw _privateConstructorUsedError;
  String get feeName => throw _privateConstructorUsedError;

  /// Serializes this ChargeDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChargeDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChargeDetailCopyWith<ChargeDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChargeDetailCopyWith<$Res> {
  factory $ChargeDetailCopyWith(
          ChargeDetail value, $Res Function(ChargeDetail) then) =
      _$ChargeDetailCopyWithImpl<$Res, ChargeDetail>;
  @useResult
  $Res call({double fee, String feeName});
}

/// @nodoc
class _$ChargeDetailCopyWithImpl<$Res, $Val extends ChargeDetail>
    implements $ChargeDetailCopyWith<$Res> {
  _$ChargeDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChargeDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fee = null,
    Object? feeName = null,
  }) {
    return _then(_value.copyWith(
      fee: null == fee
          ? _value.fee
          : fee // ignore: cast_nullable_to_non_nullable
              as double,
      feeName: null == feeName
          ? _value.feeName
          : feeName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChargeDetailImplCopyWith<$Res>
    implements $ChargeDetailCopyWith<$Res> {
  factory _$$ChargeDetailImplCopyWith(
          _$ChargeDetailImpl value, $Res Function(_$ChargeDetailImpl) then) =
      __$$ChargeDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double fee, String feeName});
}

/// @nodoc
class __$$ChargeDetailImplCopyWithImpl<$Res>
    extends _$ChargeDetailCopyWithImpl<$Res, _$ChargeDetailImpl>
    implements _$$ChargeDetailImplCopyWith<$Res> {
  __$$ChargeDetailImplCopyWithImpl(
      _$ChargeDetailImpl _value, $Res Function(_$ChargeDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChargeDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fee = null,
    Object? feeName = null,
  }) {
    return _then(_$ChargeDetailImpl(
      fee: null == fee
          ? _value.fee
          : fee // ignore: cast_nullable_to_non_nullable
              as double,
      feeName: null == feeName
          ? _value.feeName
          : feeName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChargeDetailImpl implements _ChargeDetail {
  _$ChargeDetailImpl({required this.fee, required this.feeName});

  factory _$ChargeDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChargeDetailImplFromJson(json);

  @override
  final double fee;
  @override
  final String feeName;

  @override
  String toString() {
    return 'ChargeDetail(fee: $fee, feeName: $feeName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChargeDetailImpl &&
            (identical(other.fee, fee) || other.fee == fee) &&
            (identical(other.feeName, feeName) || other.feeName == feeName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fee, feeName);

  /// Create a copy of ChargeDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChargeDetailImplCopyWith<_$ChargeDetailImpl> get copyWith =>
      __$$ChargeDetailImplCopyWithImpl<_$ChargeDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChargeDetailImplToJson(
      this,
    );
  }
}

abstract class _ChargeDetail implements ChargeDetail {
  factory _ChargeDetail(
      {required final double fee,
      required final String feeName}) = _$ChargeDetailImpl;

  factory _ChargeDetail.fromJson(Map<String, dynamic> json) =
      _$ChargeDetailImpl.fromJson;

  @override
  double get fee;
  @override
  String get feeName;

  /// Create a copy of ChargeDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChargeDetailImplCopyWith<_$ChargeDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
