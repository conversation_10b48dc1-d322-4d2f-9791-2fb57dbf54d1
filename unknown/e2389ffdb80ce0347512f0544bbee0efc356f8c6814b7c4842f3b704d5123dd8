import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';

import '../../../../../shared/constants/enums.dart';

/// 明细类型枚举
/// Enum for detail types
enum OrderType {
  /// 当前持仓
  /// Current holdings
  positions("currentPositions"),

  /// 成交明细
  /// Trade details
  trades("tradeDetails"),

  /// 委托明细
  /// Entrust details
  entrusts("entrustDetails");

  final String nameKey;

  const OrderType(this.nameKey);
}

/// 单个明细类型的状态
class OrderListState extends Equatable {
  final List<FTradeAcctOrderRecords> records;
  final int page;
  final DataStatus status;
  final bool hasMoreData; // 是否还有更多数据
  final bool isInitialLoad; // 是否为首次加载
  String get countIfNotEmpty => records.isNotEmpty ? '(${records.length})' : '';

  const OrderListState({
    this.records = const [],
    this.page = 1,
    this.status = DataStatus.idle,
    this.hasMoreData = true,
    this.isInitialLoad = true,
  });

  OrderListState copyWith({
    List<FTradeAcctOrderRecords>? records,
    int? page,
    DataStatus? status,
    bool? hasMoreData,
    bool? isInitialLoad,
  }) {
    return OrderListState(
      records: records ?? this.records,
      page: page ?? this.page,
      status: status ?? this.status,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isInitialLoad: isInitialLoad ?? this.isInitialLoad,
    );
  }

  @override
  List<Object?> get props => [
        records,
        status,
        hasMoreData,
      ];
}

/// 不可变的市场分类状态，用于 BLoC
/// Immutable state for a market category, to be used with BLoC
class MarketCategoryState extends Equatable {
  /// 市场分类枚举
  /// Market category enum
  final MarketCategory category;

  /// 各明细类型对应的子状态（含数据、页码、加载状态等）
  /// Map of detail type to its sub-state (data, page, status, etc.)
  final Map<OrderType, OrderListState> details;

  /// 当前选中的明细类型索引：0-holdings,1-trades,2-entrusts
  /// Currently selected detail type index
  final int selectedIndex;

  /// 浮动盈亏数值
  /// Floating profit and loss amount
  final double floatingPnl;

  /// 总资产数值
  /// Total assets amount
  final double totalAssets;

  const MarketCategoryState({
    required this.category,
    this.details = const {
      OrderType.positions: OrderListState(), // 当前持仓
      OrderType.trades: OrderListState(), // 成交明细
      OrderType.entrusts: OrderListState(), // 委托明细
    },
    this.selectedIndex = 0,
    this.floatingPnl = 0.0,
    this.totalAssets = 0.0,
  });

  /// 创建一个新的副本并可选择性更新部分字段
  /// Create a copy with optional updated fields
  MarketCategoryState copyWith({
    MarketCategory? category,
    Map<OrderType, OrderListState>? details,
    int? selectedIndex,
    double? floatingPnl,
    double? totalAssets,
  }) {
    return MarketCategoryState(
      category: category ?? this.category,
      details: details ?? this.details,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      floatingPnl: floatingPnl ?? this.floatingPnl,
      totalAssets: totalAssets ?? this.totalAssets,
    );
  }

  /// 根据明细类型更新对应列表并返回新状态
  /// Return new state by updating list for given detail type
  MarketCategoryState updateDetail(
    OrderType type,
    OrderListState orderListState,
  ) {
    final newDetails = Map<OrderType, OrderListState>.from(details);
    newDetails[type] = orderListState;
    return copyWith(details: newDetails);
  }

  MarketCategoryState updateIndex(int index) {
    return copyWith(selectedIndex: index);
  }

  MarketCategoryState updateDetailState(OrderType type, OrderListState detailState) {
    final newDetails = Map<OrderType, OrderListState>.from(details);
    newDetails[type] = detailState;
    return copyWith(details: newDetails);
  }

  @override
  List<Object?> get props => [
        category,
        details,
        selectedIndex,
        floatingPnl,
        totalAssets,
      ];
}
