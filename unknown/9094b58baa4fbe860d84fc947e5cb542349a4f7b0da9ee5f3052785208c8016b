import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/shared/widgets/list_tile/list_tile.dart';

import '../../../../shared/constants/enums.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';
import '../avatar/avatar_screen.dart';
import 'profile_edit_screen.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class PersonalInfoEditScreen extends StatelessWidget {
  const PersonalInfoEditScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'personalInfo'.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        child: Column(
          children: [
            14.verticalSpace,
            _AvatarSection(),
            _InfoSection(),
          ],
        ),
      ),
    );
  }
}

class _AvatarSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) =>
          previous.userData?.avatar != current.userData?.avatar || previous.updateStatus != current.updateStatus,
      builder: (context, state) {
        return GestureDetector(
          onTap: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (_) => BlocProvider.value(
                value: context.read<ProfileCubit>(),
                child: const AvatarScreen(),
              ),
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.gr),
                topRight: Radius.circular(8.gr),
              ),
              color: context.theme.cardColor,
            ),
            padding: EdgeInsets.symmetric(vertical: 16.gh),
            child: Row(
              children: [
                16.horizontalSpace,
                Text(
                  'avatar'.tr(),
                  style: context.textTheme.primary.w500,
                ),
                const Spacer(),
                if (state.updateStatus == DataStatus.loading && state.updatingField == ProfileUpdateField.avatar)
                  SizedBox(
                    width: 48.gw,
                    height: 48.gh,
                    child: Center(
                      child: SizedBox(
                        width: 24.gw,
                        height: 24.gh,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            context.theme.primaryColor,
                          ),
                        ),
                      ),
                    ),
                  )
                else
                  CircleAvatar(
                    radius: 22.gr,
                    backgroundColor: context.theme.primaryColor,
                    backgroundImage: state.userData?.avatar != null
                        ? AssetImage('assets/images/avatars/${state.userData!.avatar!}.png')
                        : null,
                    child: state.userData?.avatar == null
                        ? Icon(
                            Icons.person_outline,
                            color: context.theme.cardColor,
                            size: 24.gw,
                          )
                        : null,
                  ),
                8.horizontalSpace,
                Icon(
                  Icons.chevron_right,
                  color: context.colorTheme.textRegular,
                  size: 24.gw,
                ),
                16.horizontalSpace,
              ],
            ),
          ),
        );
      },
    );
  }
}

class _InfoSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        final userData = state.userData;

        return Column(
          children: [
            CommonListTile(
              title: 'nickname'.tr(),
              value: userData?.nickname ?? '-',
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider.value(
                    value: context.read<ProfileCubit>(),
                    child: ProfileEditScreen(
                      title: 'nickname'.tr(),
                      initialValue: userData?.nickname ?? '',
                      field: ProfileUpdateField.nickname,
                      maxLength: 20,
                    ),
                  ),
                ),
              ),
              borderRadius: 0,
            ),
            CommonListTile(
              title: 'nameOnId'.tr(),
              value: userData?.realName?.isEmpty ?? true ? '-' : userData?.realName,
              borderRadius: 0,
            ),
            // CommonListTile(
            //   title: 'gender'.tr(),
            //   value: userData?.sex != null ? _getGenderText(userData!.sex) : '-',
            //   borderRadius: 0,
            // ),
            CommonListTile(
              title: 'email'.tr(),
              value: userData?.email?.isEmpty ?? true ? '-' : userData?.email,
              borderRadius: 0,
            ),
            CommonListTile(
              title: 'phone'.tr(),
              value: userData?.mobile?.isEmpty ?? true ? '-' : userData?.mobile,
              borderRadiusGeometry: BorderRadius.only(
                bottomLeft: Radius.circular(8.gr),
                bottomRight: Radius.circular(8.gr),
              ),
            ),
          ],
        );
      },
    );
  }
}
