import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart' hide ConversationExtension;
import 'package:tencent_cloud_chat_uikit/ui/utils/common_utils.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class LastMessage extends StatelessWidget {
  const LastMessage({super.key, this.lastMessage});

  final V2TimMessage? lastMessage;

  @override
  Widget build(BuildContext context) {
    switch (lastMessage?.elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        final text = lastMessage?.textElem?.text ?? '';
        if (text.hasSquareBrackets) {
          return MessageTypeStrip(
            icon: LucideIcons.sticker,
            label: 'sticker'.tr(),
          );
        }
        return Text(
          text.fromBase64,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: context.textTheme.regular,
        );
      case MessageElemType.V2TIM_ELEM_TYPE_CUSTOM:
        final customData = lastMessage?.customElem?.data;
        if (customData != null) {
          try {
            final decodedData = json.decode(customData);
            if (decodedData['businessID'] == 'group_create') {
              return MessageTypeStrip(
                icon: LucideIcons.users,
                label: 'groupCreated'.tr(),
              );
            }
            if (decodedData['type'] == 'cm_product') {
              return MessageTypeStrip(
                icon: LucideIcons.star,
                label: 'Shared a product',
                color: context.colorTheme.stockRed,
              );
            }
            if (decodedData['type'] == 'cm_mentor') {
              return MessageTypeStrip(
                icon: LucideIcons.star,
                label: 'Shared a mentor',
                color: context.colorTheme.stockGreen,
              );
            }
          } catch (e) {
            return MessageTypeStrip(
              icon: LucideIcons.info,
              label: 'invalidCustomMessage'.tr(),
            );
          }
        }
        return MessageTypeStrip(
          icon: LucideIcons.code,
          label: 'customMessage'.tr(),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return MessageTypeStrip(icon: LucideIcons.image, label: 'image'.tr());
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return MessageTypeStrip(
          icon: LucideIcons.volume_2,
          label: 'audio'.tr(),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return MessageTypeStrip(icon: LucideIcons.video, label: 'video'.tr());
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return MessageTypeStrip(icon: LucideIcons.file, label: 'file'.tr());
      case MessageElemType.V2TIM_ELEM_TYPE_LOCATION:
        return MessageTypeStrip(
          icon: LucideIcons.map_pin,
          label: 'location'.tr(),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return MessageTypeStrip(icon: LucideIcons.smile, label: 'emoji'.tr());
      case MessageElemType.V2TIM_ELEM_TYPE_GROUP_TIPS:
        return MessageTypeStrip(
          icon: LucideIcons.info,
          label: 'groupNotification'.tr(),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_MERGER:
        return MessageTypeStrip(
          icon: LucideIcons.layers,
          label: 'mergedMessage'.tr(),
        );
      default:
        return MessageTypeStrip(
          icon: LucideIcons.info,
          label: 'unknownMessage'.tr(),
        );
    }
  }
}

class MessageTypeStrip extends StatelessWidget {
  const MessageTypeStrip({
    super.key,
    required this.icon,
    required this.label,
    this.color,
  });

  final IconData icon;
  final String label;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? context.theme.primaryColor,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            label,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 14,
              color: color ?? context.theme.primaryColor,
              fontWeight: color != null ? FontWeight.w500 : null,
            ),
          ),
        ),
      ],
    );
  }
}
