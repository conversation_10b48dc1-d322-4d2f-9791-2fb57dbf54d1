import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class DataField extends StatelessWidget {
  final String label;
  final num value;
  final String? suffix;
  final TextStyle? textStyle;
  final Color? color;
  final SelectedExchangeCubit? selectedExchangeCubit;
  final bool isCurrency;
  const DataField({
    super.key,
    required this.label,
    required this.value,
    this.selectedExchangeCubit,
    this.suffix,
    this.textStyle,
    this.color,
    this.isCurrency = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Tooltip(
            message: label,
            child: Text(
              label,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: FontPalette.normal13.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          ),
        ),
        FlipText(
          value.toDouble(),
          selectedExchangeCubit: selectedExchangeCubit,
          isCurrency: isCurrency,
          dropdownIconColor: context.theme.primaryColor,

          fractionDigits: 2,
          suffix: suffix ?? '',
          style: textStyle ??
              FontPalette.semiBold14.copyWith(
                fontFamily: 'Akzidenz-Grotesk',
                letterSpacing: -0.5,
                color: color ?? context.theme.primaryColor.withValues(alpha: 5),
              ),
        )
      ],
    );
  }
}
