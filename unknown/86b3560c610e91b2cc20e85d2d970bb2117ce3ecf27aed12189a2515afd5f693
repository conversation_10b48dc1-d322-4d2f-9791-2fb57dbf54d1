import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/withdrawal_config/withdrawal_config.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/otp/otp_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_state.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/features/account/screens/add_bank_screen.dart';
import 'package:gp_stock_app/features/account/widgets/withdraw_password_dialog.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';

import '../../../shared/routes/routes.dart';
import '../../../shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class WithdrawScreen extends StatefulWidget {
  const WithdrawScreen({super.key, this.showAppBar = true});
  final bool showAppBar;

  @override
  State<WithdrawScreen> createState() => _WithdrawScreenState();
}

class _WithdrawScreenState extends State<WithdrawScreen> {
  final amountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar
          ? AppBar(
              title: Text('bankCardWithdraw'.tr()),
              actions: [
                IconButton(
                  icon: Icon(LucideIcons.notepad_text),
                  onPressed: () {
                    Navigator.pushNamed(context, routeWithdrawRecords);
                  },
                ),
              ],
            )
          : null,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10),
              BlocBuilder<UserBankListCubit, UserBankListState>(
                builder: (context, state) {
                  return ShadowBox(
                    child: CommonDropdown(
                      hintText: 'selectWithdrawBank'.tr(),
                      onChanged: (value) {
                        final selectedBank = state.bankList?.firstWhere((e) => e.id == int.parse(value.id!));
                        context.read<UserBankListCubit>().updateSelectedBank(selectedBank);
                        context
                            .read<WithdrawalCubit>()
                            .updateSelectedBank(state.bankList?.firstWhere((e) => e.id == int.parse(value.id!)));
                      },
                      selectedItem: state.selectedBank == null
                          ? null
                          : DropDownValue(
                              id: state.selectedBank?.id.toString(),
                              value:
                                  '${state.selectedBank?.bankFullName ?? ''}(${state.selectedBank?.bankAccount ?? ''})',
                              icon: state.selectedBank?.icon,
                            ),
                      dropDownValue: (state.bankList ?? [])
                          .map((e) => DropDownValue(
                              id: e.id.toString(), value: '${e.bankFullName}(${e.bankAccount})', icon: e.icon))
                          .toList(),
                      showSearchBox: false,
                      itemBuilder: (context, item, isDisabled, isSelected) {
                        return Container(
                          margin: EdgeInsets.symmetric(horizontal: 10.0.gw),
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.0.gh),
                            child: Row(
                              children: [
                                if (item.icon != null && item.icon!.startsWith("http")) ...[
                                  Image.network(
                                    item.icon!,
                                    width: 40.gw,
                                    height: 40.gw,
                                    fit: BoxFit.cover,
                                  ),
                                ],
                                Expanded(
                                  child: Text(
                                    item.value ?? '',
                                    style: context.textTheme.regular,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Spacer(flex: 2),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => MultiBlocProvider(
                            providers: [
                              BlocProvider.value(
                                value: context.read<BankListCubit>(),
                              ),
                              BlocProvider.value(
                                value: context.read<UserBankListCubit>(),
                              ),
                              BlocProvider(create: (context) => AddBankCubit()),
                              BlocProvider(create: (context) => OtpCubit()),
                            ],
                            child: AddBankScreen(),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.add, size: 18, color: Colors.white),
                    label: Text(
                      'addBank'.tr(),
                      style: FontPalette.normal12.copyWith(color: Colors.white),
                    ),
                  )
                ],
              ),
              SizedBox(height: 10),
              BlocBuilder<UserBankListCubit, UserBankListState>(
                builder: (context, state) {
                  return ShadowBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [
                        SizedBox(height: 4),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.4.gsw,
                              child: Text(
                                'cardholder'.tr(),
                                style: FontPalette.normal14.copyWith(
                                  color: context.colorTheme.textRegular,
                                ),
                              ),
                            ),
                            Text(
                              state.selectedBank?.realName ?? '',
                              style: FontPalette.normal14.copyWith(
                                color: context.colorTheme.textRegular,
                              ),
                            ),
                          ],
                        ),
                        Divider(
                          color: context.theme.dividerColor,
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.4.gsw,
                              child: Text(
                                'cardNumber'.tr(),
                                style: FontPalette.normal14.copyWith(
                                  color: context.colorTheme.textRegular,
                                ),
                              ),
                            ),
                            Text(
                              state.selectedBank?.bankAccount ?? '',
                              style: FontPalette.normal14.copyWith(
                                color: context.colorTheme.textRegular,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4),
                      ],
                    ),
                  );
                },
              ),
              SizedBox(height: 16),
              ShadowBox(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'withdrawAmount'.tr(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: context.colorTheme.textPrimary,
                      ),
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                            child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: context.theme.scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextField(
                            controller: amountController,
                            onChanged: (value) => context.read<WithdrawalCubit>().updateWithdrawAmount(value),
                            decoration: InputDecoration(
                              hintText: 'enterWithdrawAmount'.tr(),
                              hintStyle: TextStyle(color: context.colorTheme.textRegular),
                              border: InputBorder.none,
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^[0-9.]*')),
                            ],
                          ),
                        )),
                        SizedBox(width: 8),
                        Text('¥', style: TextStyle(fontSize: 18, color: context.colorTheme.textRegular)),
                      ],
                    ),
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: BlocSelector<AccountInfoCubit, AccountInfoState, AccountInfoData?>(
                            selector: (state) => state.accountInfo,
                            builder: (context, state) {
                              return Container(
                                padding: EdgeInsets.symmetric(horizontal: 12),
                                decoration: BoxDecoration(
                                  color: context.theme.scaffoldBackgroundColor,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: TextField(
                                  enabled: false,
                                  controller: TextEditingController(text: state?.usableCash?.toString() ?? '0.00'),
                                  decoration: InputDecoration(
                                    hintText: 'accountBalance'.tr(),
                                    hintStyle: TextStyle(color: context.colorTheme.textRegular),
                                    border: InputBorder.none,
                                  ),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(RegExp(r'^[0-9.]*')),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('CNY', style: TextStyle(fontSize: 18, color: context.colorTheme.textRegular)),
                      ],
                    ),
                    SizedBox(height: 12),
                    BlocSelector<WithdrawalCubit, WithdrawalState, WithdrawalConfig?>(
                      selector: (state) => state.withdrawalConfig,
                      builder: (context, state) {
                        if (state == null) return const SizedBox.shrink();
                        final minWithdrawalAmount = state.minWithdrawalAmount;
                        final maxWithdrawalAmount = state.maxWithdrawalAmount;
                        return Text(
                          '*${'thatChannelMinimumDeposit'.tr()} $minWithdrawalAmount, ${'thatChannelMaximumDeposit'.tr()} $maxWithdrawalAmount',
                          style: TextStyle(fontSize: 12, color: context.colorTheme.textRegular),
                        );
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: BlocListener<WithdrawalCubit, WithdrawalState>(
                      listenWhen: (previous, current) => previous.withdrawStatus != current.withdrawStatus,
                      listener: (context, state) {
                        if (state.withdrawStatus.isSuccess) {
                          Helper.showFlutterToast('withdrawnSuccessfully'.tr());
                          Navigator.pop(context);
                        } else if (state.withdrawStatus.isFailed) {
                          Helper.showFlutterToast(state.error ?? 'invalidWithdrawPassword'.tr());
                        }
                      },
                      child: BlocSelector<WithdrawalCubit, WithdrawalState,
                          ({WithdrawalConfig? config, bool isFormValid, bool isLoading, double? usableCash})>(
                        selector: (state) => (
                          config: state.withdrawalConfig,
                          isFormValid: state.isFormValid ?? false,
                          isLoading: state.withdrawStatus.isLoading,
                          usableCash: context.read<AccountInfoCubit>().state.accountInfo?.usableCash,
                        ),
                        builder: (context, state) {
                          return CustomMaterialButton(
                            onPressed: state.isFormValid
                                ? () {
                                    final amount = double.tryParse(amountController.text) ?? 0;
                                    final config = state.config;
                                    final usableCash = state.usableCash ?? 0;

                                    if (config == null) return;

                                    if (amount < config.minWithdrawalAmount) {
                                      Helper.showFlutterToast(
                                          '${'thatChannelMinimumDeposit'.tr()} ${config.minWithdrawalAmount}');
                                      return;
                                    }

                                    if (amount > config.maxWithdrawalAmount) {
                                      Helper.showFlutterToast(
                                          '${'thatChannelMaximumDeposit'.tr()} ${config.maxWithdrawalAmount}');
                                      return;
                                    }

                                    if (amount > usableCash) {
                                      Helper.showFlutterToast('insufficientBalance'.tr());
                                      return;
                                    }

                                    showDialog(
                                      context: context,
                                      builder: (_) => BlocProvider.value(
                                        value: context.read<WithdrawalCubit>(),
                                        child: WithdrawPasswordDialog(
                                          withdrawAmount: amountController.text,
                                          userBankId: context.read<UserBankListCubit>().state.selectedBank!.id,
                                        ),
                                      ),
                                    );
                                  }
                                : null,
                            isLoading: state.isLoading,
                            buttonText: 'submit'.tr(),
                            isEnabled: state.isFormValid,
                            color: context.theme.primaryColor,
                            borderColor: context.theme.primaryColor,
                            borderRadius: 5.gr,
                            textColor: Colors.white,
                            fontSize: 13.gr,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              SupportWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
