import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/home/<USER>/models/banner/banner_response.dart';
import 'package:gp_stock_app/features/home/<USER>/home/<USER>';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class StyleAHomeBanner extends StatefulWidget {
  final DataStatus dataStatus;
  final List<BannerData> list;
  final Function(int) onPressed;
  const StyleAHomeBanner({super.key, required this.dataStatus, required this.list, required this.onPressed});

  @override
  State<StyleAHomeBanner> createState() => _StyleAHomeBannerState();
}

class _StyleAHomeBannerState extends State<StyleAHomeBanner> {
  final CarouselSliderController carouselController = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double bannerHeight = (screenWidth - 20) * 126 / 355;

    if (widget.dataStatus == DataStatus.loading) {
      return ShimmerWidget(
        width: double.infinity,
        height: bannerHeight,
        radius: 4,
      );
    }

    return Stack(
      children: [
        CarouselSlider.builder(
          options: CarouselOptions(
            aspectRatio: 355 / 126,
            viewportFraction: 1,
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            enableInfiniteScroll: true,
            onPageChanged: (index, reason) => context.read<HomeCubit>().updateBannerIndex(index),
          ),
          itemCount: widget.list.length,
          itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) => InkWell(
            onTap: () {
              widget.onPressed(itemIndex);
            },
            child: Container(
              width: double.infinity,
              height: bannerHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(widget.list[itemIndex].imageUrl ?? ''),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          carouselController: carouselController,
        ),
      ],
    );
  }
}
