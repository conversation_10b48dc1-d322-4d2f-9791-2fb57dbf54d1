import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class Dp extends StatelessWidget {
  const Dp({
    super.key,
    this.name,
    this.userId,
    this.faceUrl,
    this.type,
    this.size = 55,
  });

  final String? faceUrl;
  final int? type;
  final double? size;
  final String? userId;
  final String? name;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        ClipOval(
          child: Container(
            decoration: BoxDecoration(
              gradient: userId.gradientFromUserId,
            ),
            width: size,
            height: size,
            child: faceUrl == null || faceUrl!.isEmpty
                ? _buildFallbackWidget()
                : CachedNetworkImage(
                    imageUrl: faceUrl!,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => _buildFallbackWidget(),
                  ),
          ),
        ),
        if (type == 2)
          Positioned(
            bottom: 0,
            right: -5,
            child: CircleAvatar(
              radius: 12,
              backgroundColor: context.theme.scaffoldBackgroundColor,
              child: Container(
                decoration: BoxDecoration(
                  color: context.theme.primaryColor,
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(4),
                child: Icon(Icons.group, color: context.theme.scaffoldBackgroundColor, size: 14),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFallbackWidget() {
    final text = name.notNullNorEmpty ? name : 'Test';
    return Center(
      child: Text(
        text?.substring(0, 1).toUpperCase() ?? '',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
