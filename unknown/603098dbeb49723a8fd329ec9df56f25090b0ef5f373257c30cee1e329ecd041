import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../logic/account/account_cubit.dart';

class AssetsCard extends StatelessWidget {
  final double totalAssets;
  final double todayEarnings;
  final double availableBalance;
  final double myInterest;
  final double frozenAmount;
  final bool isContract;
  final String currency;
  final String? assetTitle;
  final String? profitTitle;

  const AssetsCard._({
    super.key,
    required this.totalAssets,
    required this.todayEarnings,
    required this.availableBalance,
    required this.myInterest,
    required this.frozenAmount,
    required this.isContract,
    required this.currency,
    required this.assetTitle,
    required this.profitTitle,
  });

  factory AssetsCard({
    Key? key,
    double? totalAssets,
    double? todayEarnings,
    double? availableBalance,
    double? myInterest,
    double? frozenAmount,
    bool? isContract,
    String? currency,
    String? assetTitle,
    String? profitTitle,
  }) {
    return AssetsCard._(
      key: key,
      totalAssets: totalAssets ?? 0,
      todayEarnings: todayEarnings ?? 0,
      availableBalance: availableBalance ?? 0,
      myInterest: myInterest ?? 0,
      frozenAmount: frozenAmount ?? 0,
      isContract: isContract ?? false,
      currency: currency ?? 'CNY',
      assetTitle: assetTitle,
      profitTitle: profitTitle,
    );
  }

  @override
  Widget build(BuildContext context) {
    final exchangeRates = context.read<ExchangeRateCubit>().getRates();
    final selectedExchangeCubit = getIt<SelectedExchangeCubit>()
      ..updateSelectedExchangeRate(exchangeRates.firstWhere((rate) => rate.currencyTarget == currency));

    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return Container(
          height: 160.gh,
          width: 347.gw,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.theme.primaryColorLight,
                context.theme.primaryColor,
              ],
            ),
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.gw, 22.gw, 16.gw, 0),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            assetTitle ?? 'totalAssets'.tr(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.gsp,
                            ),
                          ),
                          Text(
                              profitTitle ?? 'unrealizedPnl'.tr(),
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14.gsp,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8.gh),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          FlipText(
                            totalAssets,
                            showCurrencyDropdown: !isContract,
                            isCurrency: true,
                            suffix: isContract ? ' $currency' : null,
                            selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                            style: context.textTheme.secondary,
                            dropdownIconColor: context.colorTheme.buttonPrimary,
                          ),
                          FlipText(
                            todayEarnings,
                            isCurrency: true,
                            selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                            suffix: '',
                            style: FontPalette.bold18.copyWith(color: ColorPalette.whiteColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                height: 64.gh,
                // padding: EdgeInsets.symmetric(vertical: 10.gh),
                color: Colors.white.withNewOpacity(0.1),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _BuildBalanceItem(
                      label: 'availableBalance'.tr(),
                      amount: availableBalance,
                      selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                    ),
                    _Divider(),
                    if (!isContract) ...[
                      _BuildBalanceItem(
                        label: 'interest'.tr(),
                        amount: myInterest,
                      ),
                      _Divider(),
                    ],
                    _BuildBalanceItem(
                      label: 'frozenAmount'.tr(),
                      amount: frozenAmount,
                      selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class _BuildBalanceItem extends StatelessWidget {
  const _BuildBalanceItem({
    required this.label,
    required this.amount,
    this.selectedExchangeCubit,
  });

  final String label;
  final num amount;
  final SelectedExchangeCubit? selectedExchangeCubit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12.gsp,
          ),
        ),
        FlipText(
          amount.toDouble(),
          fractionDigits: 2,
          isCurrency: true,
          suffix: '',
          style: FontPalette.normal16.copyWith(
            color: ColorPalette.whiteColor,
            fontFamily: 'Akzidenz-Grotesk',
          ),
          selectedExchangeCubit: selectedExchangeCubit,
        )
      ],
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1,
      height: 30.gh,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white24,
            Colors.white,
            Colors.white24,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }
}
