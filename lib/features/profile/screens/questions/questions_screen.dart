import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/widgets/questions/shimmer_widget.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/debouncer.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/text_fields/text_field_widget.dart';
import '../../logic/help/help_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';

class QuestionsScreen extends StatelessWidget {
  const QuestionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HelpCubit()..getHelpList(),
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: SettingsAppBar(title: 'newbieQuestions'.tr()),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.gw),
          child: Column(
            children: [
              10.verticalSpace,
              _SearchBar(),
              10.verticalSpace,
              Expanded(
                child: BlocBuilder<HelpCubit, HelpState>(
                  builder: (context, state) {
                    if (state.searchStatus == DataStatus.loading) {
                      return QuestionShimmerWidget();
                    } else if (state.searchStatus == DataStatus.success &&
                        state.searchResults != null &&
                        state.query?.isNotEmpty == true) {
                      return ListView.builder(
                        itemCount: state.searchResults!.length,
                        itemBuilder: (context, index) {
                          final question = state.searchResults![index];
                          return Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: context.appTheme.cardColor,
                                  borderRadius: BorderRadius.circular(8.gr),
                                ),
                                child: ListTile(
                                  title: Text(
                                    question.title,
                                    style: FontPalette.light14.copyWith(
                                      color: context.appTheme.textColor2,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                  onTap: () =>
                                      Navigator.pushNamed(context, routeQuestionsDetails, arguments: question.id),
                                  trailing: Icon(
                                    Icons.chevron_right,
                                    color: context.appTheme.subTitleColor,
                                    size: 24.gr,
                                  ),
                                  contentPadding: EdgeInsets.symmetric(horizontal: 12.gw),
                                ),
                              ),
                              12.verticalSpace,
                            ],
                          );
                        },
                      );
                    } else if (state.helpListStatus == DataStatus.loading) {
                      return QuestionShimmerWidget();
                    } else if (state.helpListStatus == DataStatus.failed) {
                      return Center(child: Text(state.error ?? 'Error loading data'));
                    }
                    return ListView.builder(
                      itemCount: state.helpCategories?.length ?? 0,
                      itemBuilder: (context, index) {
                        final category = state.helpCategories![index];
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 12.gh),
                              decoration: BoxDecoration(
                                color: context.appTheme.cardColor,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.gr),
                                  topRight: Radius.circular(8.gr),
                                ),
                              ),
                              child: Text(
                                category.name,
                                style: FontPalette.bold15.copyWith(
                                  color: context.appTheme.titleColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Divider(
                              color: context.appTheme.cardColor,
                              height: 1,
                              thickness: 0.1,
                            ),
                            ListView.separated(
                              shrinkWrap: true,
                              separatorBuilder: (context, index) => Divider(
                                color: context.appTheme.cardColor,
                                height: 1,
                                thickness: 0.1,
                              ),
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: category.questions.length,
                              itemBuilder: (context, qIndex) {
                                final question = category.questions[qIndex];
                                return Container(
                                  decoration: BoxDecoration(
                                    color: context.appTheme.cardColor,
                                    borderRadius: qIndex == (category.questions.length - 1)
                                        ? BorderRadius.only(
                                            bottomLeft: Radius.circular(8.gr),
                                            bottomRight: Radius.circular(8.gr),
                                          )
                                        : null,
                                  ),
                                  child: ListTile(
                                    title: Text(
                                      question.title,
                                      style: FontPalette.light14.copyWith(
                                        color: context.appTheme.textColor2,
                                        fontWeight: FontWeight.normal,
                                      ),
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: qIndex == (category.questions.length - 1)
                                          ? BorderRadius.only(
                                              bottomLeft: Radius.circular(8.gr),
                                              bottomRight: Radius.circular(8.gr),
                                            )
                                          : BorderRadius.zero,
                                    ),
                                    onTap: () =>
                                        Navigator.pushNamed(context, routeQuestionsDetails, arguments: question.id),
                                    trailing: Icon(
                                      Icons.chevron_right,
                                      color: context.appTheme.subTitleColor,
                                      size: 24.gr,
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 12.gw),
                                  ),
                                );
                              },
                            ),
                            12.verticalSpace,
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SearchBar extends StatelessWidget {
  final _debouncer = Debouncer(milliseconds: 500);

  @override
  Widget build(BuildContext context) {
    return TextFieldWidget(
      hintText: 'searchKeywords'.tr(),
      prefixIcon: SvgPicture.asset(
        Assets.searchIcon,
        fit: BoxFit.scaleDown,
        width: 18.gw,
        height: 18.gh,
      ),
      fillColor: context.appTheme.cardColor,
      onChanged: (value) {
        _debouncer.run(() {
          context.read<HelpCubit>().searchHelpQuestions(value!);
        });
      },
    );
  }
}
