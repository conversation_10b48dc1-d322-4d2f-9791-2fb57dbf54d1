import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/services/app_info_service.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/widgets/about_us/about_us_shimmer.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';

import '../../../../core/dependency_injection/injectable.dart';
import '../../../../shared/constants/assets.dart';
import '../../../../shared/constants/constants.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/list_tile/list_tile.dart';
import '../../logic/app_info/app_info_cubit.dart';
import '../../logic/app_info/app_info_state.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'aboutUs'.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        child: SingleChildScrollView(
          child: Column(
            children: [
              32.verticalSpace,
              // App Logo and Info Section
              _buildAppInfoSection(context),
              32.verticalSpace,
              // Menu Items
              BlocBuilder<AppInfoCubit, AppInfoState>(
                builder: (context, state) {
                  if (state.status == DataStatus.loading) {
                    return const AboutUsShimmer();
                  }
                  if (state.status == DataStatus.failed) {
                    return const SizedBox.shrink();
                  }
                  return _buildMenuItems(context, state.appInfoList);
                },
              ),
              32.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppInfoSection(BuildContext context) {
    return Column(
      children: [
        // App Logo
        Container(
          width: 60.gw,
          height: 60.gw,
          decoration: BoxDecoration(
            color: context.theme.primaryColor,
            borderRadius: BorderRadius.circular(16.gr),
            boxShadow: [
              BoxShadow(
                color: context.theme.primaryColor.withValues(alpha: 0.5),
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(16.gw),
            child: SvgPicture.asset(
              Assets.appLogo,
              colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
            ),
          ),
        ),
        16.verticalSpace,
        // App Name
        FutureBuilder<String>(
          future: getIt<AppInfoService>().getAppName(),
          builder: (context, snapshot) {
            return Text(
              snapshot.data ?? kAppName,
              style: context.textTheme.primary.fs18.w600,
              textAlign: TextAlign.center,
            );
          },
        ),
        // App Version
        FutureBuilder<String>(
          future: getIt<AppInfoService>().getAppVersion(),
          builder: (context, snapshot) {
            return Text(
              snapshot.data ?? kAppVersion,
              style: context.textTheme.regular,
              textAlign: TextAlign.center,
            );
          },
        ),
      ],
    );
  }

  Widget _buildMenuItems(BuildContext context, List appInfoList) {
    if (appInfoList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: appInfoList.asMap().entries.map((entry) {
        final index = entry.key;
        final info = entry.value;
        final isFirst = index == 0;
        final isLast = index == appInfoList.length - 1;

        BorderRadiusGeometry? borderRadius;
        if (appInfoList.length == 1) {
          borderRadius = BorderRadius.circular(8.gr);
        } else if (isFirst) {
          borderRadius = BorderRadius.only(
            topLeft: Radius.circular(8.gr),
            topRight: Radius.circular(8.gr),
          );
        } else if (isLast) {
          borderRadius = BorderRadius.only(
            bottomLeft: Radius.circular(8.gr),
            bottomRight: Radius.circular(8.gr),
          );
        } else {
          borderRadius = BorderRadius.zero;
        }

        return CommonListTile(
          title: info.title,
          onTap: () => Navigator.pushNamed(
            context,
            routeAppInfoContent,
            arguments: info,
          ),
          showBorder: !isLast,
          borderRadiusGeometry: borderRadius,
        );
      }).toList(),
    );
  }
}
