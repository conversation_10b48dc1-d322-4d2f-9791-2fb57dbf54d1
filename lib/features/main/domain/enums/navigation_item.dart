import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_page_v2.dart';
import 'package:gp_stock_app/features/activity/screens/activity_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_screen.dart';
import 'package:gp_stock_app/features/market/market_section_screen.dart';
import 'package:gp_stock_app/features/profile/screens/profile_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';


enum NavigationItem {
  home(
    page: StyleAHomeScreen(), // HomeScreen(),
    icon: Assets.homeIcon,
    label: 'home',
  ),
  account(
    page: AccountScreenV2(),
    // page: AccountScreen(),
    icon: Assets.accountIcon,
    label: 'account',
  ),
  trade(
    page: MarketSectionScreen(),
    // page: AccountScreen(),
    icon: Assets.tradeIcon,
    label: 'quote',
  ),
  activity(
    page: ActivityScreen(),
    icon: Assets.activityIcon,
    label: 'activity',
  ),
  profile(
    page: ProfileScreen(),
    icon: Assets.profileIcon,
    label: 'profile',
    showAppBar: false,
  );

  const NavigationItem({
    required this.page,
    required this.icon,
    required this.label,
    this.showAppBar = true,
  });

  final Widget page;
  final String icon;
  final String label;
  final bool showAppBar;
}
