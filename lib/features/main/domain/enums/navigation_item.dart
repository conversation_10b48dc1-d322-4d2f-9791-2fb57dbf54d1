import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_page_v2.dart';
import 'package:gp_stock_app/features/activity/screens/activity_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen.dart';
import 'package:gp_stock_app/features/market/market_section_screen.dart';
import 'package:gp_stock_app/features/profile/screens/profile_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';

import '../../../account/screens/account_screen.dart';

enum NavigationItem {
  home(
    page: HomeScreen(),
    icon: Assets.homeIcon,
    selectedIcon: Assets.homeIconFilled,
    label: 'home',
  ),
  account(
    page: AccountScreenV2(),
    // page: AccountScreen(),
    icon: Assets.accountIcon,
    selectedIcon: Assets.accountIconFilled,
    label: 'account',
  ),
  trade(
    page: MarketSectionScreen(),
    // page: AccountScreen(),
    icon: Assets.tradeIcon,
    selectedIcon: Assets.tradeIconFilled,
    label: 'quote',
  ),
  activity(
    page: ActivityScreen(),
    icon: Assets.activityIcon,
    selectedIcon: Assets.activityIconFilled,
    label: 'activity',
  ),
  profile(
    page: ProfileScreen(),
    icon: Assets.profileIcon,
    selectedIcon: Assets.profileIconFilled,
    label: 'profile',
    showAppBar: false,
  );

  const NavigationItem({
    required this.page,
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.showAppBar = true,
  });

  final Widget page;
  final String icon;
  final String selectedIcon;
  final String label;
  final bool showAppBar;
}
