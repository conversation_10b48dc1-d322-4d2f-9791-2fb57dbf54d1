import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/api/network/endpoint/urls.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/debouncer.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/main/widgets/draggable_float_widget.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:gp_stock_app/shared/widgets/app_bar/app_bar.dart';
import 'package:gp_stock_app/shared/widgets/snackbar/snackbar_helper.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with SingleTickerProviderStateMixin, RouteAware {
  late final PageController _pageController;
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;

  final _doubleTapHandler = DoubleTapHandler();
  OverlayEntry? _overlayEntry;
  bool _showChatFloatWidget = true;
  bool _isFirstBuild = true;

  // Subscriptions for socket events
  final List<StreamSubscription> _socketSubscriptions = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
    _initializeServices();
    _setupInitialState();
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    final webSocketService = getIt<WebSocketService>();

    // Listen for warning events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.warning).listen(_handleWarningMessage));

    // Listen for close events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.close).listen(_handleWarningMessage));

    // Listen for system events
    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.system).listen(_handleSystemMessage));

    _socketSubscriptions.add(webSocketService.onMessageWithType(SocketEvents.auth).listen(_handleAuth));
  }

  void _handleWarningMessage(WebSocketMessage message) {
    // Extract content from message data
    final data = message.toJson();
    final content = data?['data']?['content'] as String? ?? '_warning'.tr();

    // Show warning notification
    GPEasyLoading.showToast(content);
  }

  void _handleSystemMessage(WebSocketMessage message) {
    // Extract content from message data
    final data = message.toJson();
    final content = data?['data']?['content'] as String? ?? 'System notification';

    // Show system notification
    GPEasyLoading.showToast(content);
  }

  void _initializeControllers() {
    _pageController = PageController(initialPage: 0, keepPage: true);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _setupAnimations() {
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  void _initializeServices() {
    context.read<MainCubit>().selectedNavigationItem(NavigationItem.home);
    context.read<MainCubit>().sendLocale(navigatorKey.currentContext!.locale);

    final webSocketService = getIt<WebSocketService>();
    webSocketService
      ..connect("${HostUtil().currentHost ?? Urls.marketWs}/ws".replaceAll("https", "wss").replaceAll("http", "wss"))
      ..setLogging(false);
  }

  void _setupInitialState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showFloatingChat();
      if (_isFirstBuild) {
        _animationController.forward();
        _isFirstBuild = false;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    setupRouteObserver();
  }

  void setupRouteObserver() {
    final route = ModalRoute.of(context);
    if (route is! PageRoute) return;

    final observers = Navigator.of(context).widget.observers;
    final routeObserver = observers.whereType<RouteObserver<ModalRoute<void>>>().firstOrNull;
    routeObserver?.subscribe(this, route);
  }

  @override
  void didPopNext() {
    if (_showChatFloatWidget && _overlayEntry == null) {
      _showFloatingChat();
    }
  }

  void _hideFloatingChat() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _showFloatingChat() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => DraggableFloatWidget(
        config: DraggableFloatWidgetConfig(
          marginTop: MediaQuery.of(context).padding.top,
          marginBottom: 100.gh,
          initialPosition: FloatingPosition.bottomRight,
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
  }

  void handlePopScope(bool didPop) {
    if (didPop) return;

    final shouldExit = _doubleTapHandler.handle();
    if (shouldExit) {
      SystemNavigator.pop();
    } else {
      showAppSnackBar(
        'pressAgainToExit'.tr(),
        context,
        icon: Icons.exit_to_app,
      );
    }
  }

  void _handleAuth(WebSocketMessage message) {
    final data = message.toJson();
    if (data?['code'] == 401) {
      Helper.logoutUser(navigatorKey.currentContext!, needNav: false);
      NetworkHelper.handleMessage(
        'sessionExpired'.tr(),
        navigatorKey.currentContext!,
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        onTap: () {
          Navigator.of(navigatorKey.currentContext!).pushNamedAndRemoveUntil(routeLogin, (route) => route.isFirst);
        },
      );
    }
  }

  void handlePageChange(int index) {
    // This piece of code is duplicated [onDestinationSelected]
  }

  Widget buildNavigationBar(MainState state) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.gr)),
      child: NavigationBar(
        backgroundColor: context.theme.cardColor,
        elevation: 1,
        onDestinationSelected: (index) {
          if (index == 1) {
            context.verifyAuth(() {
              context.read<MainCubit>().selectedNavigationItem(NavigationItem.values[1]);
            });
          } else {
            context.read<MainCubit>().selectedNavigationItem(NavigationItem.values[index]);
          }
        },
        indicatorColor: Colors.transparent,
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        selectedIndex: state.selectedNavigationItem.index,
        destinations: NavigationItem.values.map(buildNavigationDestination).toList(),
      ),
    );
  }

  NavigationDestination buildNavigationDestination(NavigationItem item) {
    return NavigationDestination(
      enabled: true,
      icon: IconHelper.loadAsset(item.icon, color: context.colorTheme.textRegular),
      selectedIcon: IconHelper.loadAsset(item.icon, shouldEnableThemeGradient: true),
      label: item.label.tr(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BlocListener<MainCubit, MainState>(
          listener: (context, state) {
            _pageController.jumpToPage(state.selectedNavigationItem.index);
          },
          child: BlocBuilder<MainCubit, MainState>(
            builder: (context, state) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_showChatFloatWidget != state.showChatFloatWidget) {
                  _showChatFloatWidget = state.showChatFloatWidget;
                  _showChatFloatWidget ? _showFloatingChat() : _hideFloatingChat();
                }
              });

              return PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, _) => handlePopScope(didPop),
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.1),
                      end: Offset.zero,
                    ).animate(_animationController),
                    child: Scaffold(
                      appBar: state.selectedNavigationItem.showAppBar
                          ? const PreferredSize(
                              preferredSize: Size.fromHeight(kToolbarHeight),
                              child: MainTitle(),
                            )
                          : null,
                      body: PageView(
                        onPageChanged: handlePageChange,
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: NavigationItem.values.map((item) => item.page).toList(),
                      ),
                      extendBody: true,
                      bottomNavigationBar: FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _fadeAnimation,
                          child: buildNavigationBar(state),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _hideFloatingChat();
    _pageController.dispose();
    _doubleTapHandler.dispose();
    _animationController.dispose();

    // Cancel all socket subscriptions
    for (var subscription in _socketSubscriptions) {
      subscription.cancel();
    }
    super.dispose();
  }
}
