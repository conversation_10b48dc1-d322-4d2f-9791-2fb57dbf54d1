import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

part 'index_stock.freezed.dart';
part 'index_stock.g.dart';

@freezed
class IndexStock with _$IndexStock {
  const IndexStock._();
  const factory IndexStock({
    @Default('') String createTime,
    @Default('') String currency,
    @Default(0) int id,
    @Default(true) bool isAllowTrade,
    @Default(true) bool isTimerClose,
    @JsonKey(name: 'isToShort') @Default(true) bool allowShortSell,
    @Default(0) double lotSize,
    @Default('') String market,
    @Default('') String securityType,
    @Default(0) int siteId,
    @Default('') String symbol,
    @Default('') String timerValue,
    @Default('') String updateTime,
    @Default(0) int tradeUnit,
  }) = _IndexStock;

  factory IndexStock.fromJson(Map<String, dynamic> json) => _$IndexStockFromJson(json);

  String get instrument => '$market|$securityType|$symbol';

  Instrument get instrumentObj => Instrument(instrument: instrument);
}
