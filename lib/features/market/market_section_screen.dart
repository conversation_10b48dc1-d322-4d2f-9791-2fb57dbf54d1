import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/index_trade_screen.dart';
import 'package:gp_stock_app/features/market/stock_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/screens/watch_list_screen.dart';
import 'package:gp_stock_app/shared/widgets/tab/custom_tab_bar.dart';

import '../../core/dependency_injection/injectable.dart';
import '../../shared/mixin/animation.dart';
import '../futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import '../futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import '../futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import '../main/logic/main/main_cubit.dart';
import 'logic/market/market_cubit.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;
  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          BlocSelector<MarketCubit, MarketState, int>(
            selector: (state) => state.mainHeaderIndex,
            builder: (context, state) {
              return CustomTabBar(
                tabs: AppConfig.instance.needShowFutureTrade
                    ? ['marketTitle6'.tr(), 'marketTitle3'.tr(), 'marketTitle7'.tr(), 'marketTitle1'.tr()]
                    : ['marketTitle6'.tr(), 'marketTitle3'.tr(), 'marketTitle1'.tr()],
                selectedIndex: state,
                onTabSelected: (index) => context.read<MarketCubit>().updateMainHeaderTab(index),
              );
            },
          ),
          Expanded(
            child: BlocSelector<MarketCubit, MarketState, int>(
              selector: (state) => state.mainHeaderIndex,
              builder: (context, state) {
                switch (state) {
                  case 0:
                    return StockScreen(
                      showBackButton: true,
                      scrollController: widget.scrollController,
                    );
                  case 1:
                    return IndexTradeScreen();
                  case 2:
                    return MultiBlocProvider(
                      providers: [
                        BlocProvider<FTradeListCubit>(
                          create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: false),
                        ),
                        BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
                      ],
                      child: FTradeListScreen(showInHomePage: false),
                    );
                  case 3:
                    return const WatchListScreen();
                  default:
                    return const StockScreen();
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
