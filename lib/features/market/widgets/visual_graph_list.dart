import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/stock/stock_response.dart';
import 'package:gp_stock_app/shared/widgets/market_table/market_table_row.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class VisualGraphList extends StatefulWidget {
  const VisualGraphList({super.key});

  @override
  State<VisualGraphList> createState() => _VisualGraphListState();
}

class _VisualGraphListState extends State<VisualGraphList> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IndexTradeCubit, IndexTradeState>(
      builder: (context, state) {
        int itemCount = state.indexStocks.length;

        if (state.status == DataStatus.loading) {
          return _buildLoadingList();
        }

        if (state.status.isFailed) {
          return const Center(child: TableEmptyWidget());
        }

        if (state.status == DataStatus.success && itemCount <= 0) {
          return const Center(child: TableEmptyWidget());
        }

        if (itemCount > 5) {
          itemCount = 5;
        }

        return ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: itemCount,
          itemBuilder: (context, index) {
            final item = state.indexStocks[index].stockInfo.data!;
            return MarketTableRow(
              data: StockItem(
                name: item.name,
                symbol: item.symbol,
                latestPrice: item.latestPrice,
                market: item.market,
                gain: item.gain,
                securityType: item.securityType,
              ),
              tabType: TradeTabType.Quotes,
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gh),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gh,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }
}
