// To parse this JSON data, do
//
//     final orderResponse = orderResponseFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

part 'order_response.freezed.dart';
part 'order_response.g.dart';

OrderResponse orderResponseFromJson(str) => OrderResponse.fromJson((str));

String orderResponseToJson(OrderResponse data) => json.encode(data.toJson());

@freezed
class OrderResponse with _$OrderResponse {
  const factory OrderResponse({
    int? code,
    OrderData? data,
    String? msg,
    String? sign,
  }) = _OrderResponse;

  factory OrderResponse.fromJson(Map<String, dynamic> json) => _$OrderResponseFromJson(json);
}

@freezed
class OrderData with _$OrderData {
  const factory OrderData({
    int? current,
    bool? hasNext,
    List<OrderRecord>? records,
    int? total,
  }) = _OrderData;

  factory OrderData.fromJson(Map<String, dynamic> json) => _$OrderDataFromJson(json);
}

@freezed
class OrderRecord with _$OrderRecord {
  const OrderRecord._();
  const factory OrderRecord({
    String? cancelTime,
    String? currency,
    double? dealNum,
    double? dealPrice,
    String? dealTime,
    int? direction,
    int? id,
    String? market,
    int? priceType,
    int? status,
    String? symbol,
    String? symbolName,
    double? tradeNum,
    double? tradePrice,
    String? tradeTime,
    int? tradeType,
    double? transactionAmount,
    int? type,
    double? availableMargin,
    double? marginAmount,
    double? buyAvgPrice,
    double? buyTotalNum,
    double? costPrice,
    double? disableNum,
    double? floatingProfitLoss,
    double? floatingProfitLossRate,
    double? marketValue,
    double? restNum,
    double? takeProfitValue,
    double? stopLossValue,
    double? appendMargin,
    double? marginRatio,
    double? warningLine,
    double? distanceWarningLine,
    double? closeLine,
    double? distanceCloseLine,
    double? feeAmount,
    int? positionDays,
    double? positionTotalNum,
    String? securityType,
    String? createTime,
    double? stockPrice,
    int? contractAccountId,
    int? periodType,
    int? multiple,
    double? winAmount,
    double? tradeFee,
    int? contractId,
    int? contractType,
    List<ChargeDetail>? chargeDetailList,
  }) = _OrderRecord;

  factory OrderRecord.fromJson(Map<String, dynamic> json) => _$OrderRecordFromJson(json);

  String get instrument => '$market|$securityType|$symbol';
  Instrument get instrumentInfo => Instrument(instrument: instrument);
  bool get isIndex => securityType == '2';
}

@freezed
class ChargeDetail with _$ChargeDetail {
  factory ChargeDetail({
    required double fee,
    required String feeName,
  }) = _ChargeDetail;

  factory ChargeDetail.fromJson(Map<String, dynamic> json) => _$ChargeDetailFromJson(json);
}

class OrderListByStatus {
  final List<OrderRecord> activeOrders;
  final List<OrderRecord> completedOrders;
  final List<OrderRecord> canceledOrders;

  const OrderListByStatus({
    required this.activeOrders,
    required this.completedOrders,
    required this.canceledOrders,
  });

  factory OrderListByStatus.fromJson(Map<String, dynamic> json) {
    return OrderListByStatus(
      activeOrders: (json['activeOrders'] as List<dynamic>?)
              ?.map((e) => OrderRecord.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      completedOrders: (json['completedOrders'] as List<dynamic>?)
              ?.map((e) => OrderRecord.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      canceledOrders: (json['canceledOrders'] as List<dynamic>?)
              ?.map((e) => OrderRecord.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activeOrders': activeOrders.map((e) => e.toJson()).toList(),
      'completedOrders': completedOrders.map((e) => e.toJson()).toList(),
      'canceledOrders': canceledOrders.map((e) => e.toJson()).toList(),
    };
  }
}
