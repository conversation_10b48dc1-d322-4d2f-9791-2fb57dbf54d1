// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderResponseImpl _$$OrderResponseImplFromJson(Map<String, dynamic> json) =>
    _$OrderResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : OrderData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
      sign: json['sign'] as String?,
    );

Map<String, dynamic> _$$OrderResponseImplToJson(_$OrderResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
      'sign': instance.sign,
    };

_$OrderDataImpl _$$OrderDataImplFromJson(Map<String, dynamic> json) =>
    _$OrderDataImpl(
      current: (json['current'] as num?)?.toInt(),
      hasNext: json['hasNext'] as bool?,
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => OrderRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$OrderDataImplToJson(_$OrderDataImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'hasNext': instance.hasNext,
      'records': instance.records,
      'total': instance.total,
    };

_$OrderRecordImpl _$$OrderRecordImplFromJson(Map<String, dynamic> json) =>
    _$OrderRecordImpl(
      cancelTime: json['cancelTime'] as String?,
      currency: json['currency'] as String?,
      dealNum: (json['dealNum'] as num?)?.toDouble(),
      dealPrice: (json['dealPrice'] as num?)?.toDouble(),
      dealTime: json['dealTime'] as String?,
      direction: (json['direction'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      market: json['market'] as String?,
      priceType: (json['priceType'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      symbol: json['symbol'] as String?,
      symbolName: json['symbolName'] as String?,
      tradeNum: (json['tradeNum'] as num?)?.toDouble(),
      tradePrice: (json['tradePrice'] as num?)?.toDouble(),
      tradeTime: json['tradeTime'] as String?,
      tradeType: (json['tradeType'] as num?)?.toInt(),
      transactionAmount: (json['transactionAmount'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      availableMargin: (json['availableMargin'] as num?)?.toDouble(),
      marginAmount: (json['marginAmount'] as num?)?.toDouble(),
      buyAvgPrice: (json['buyAvgPrice'] as num?)?.toDouble(),
      buyTotalNum: (json['buyTotalNum'] as num?)?.toDouble(),
      costPrice: (json['costPrice'] as num?)?.toDouble(),
      disableNum: (json['disableNum'] as num?)?.toDouble(),
      floatingProfitLoss: (json['floatingProfitLoss'] as num?)?.toDouble(),
      floatingProfitLossRate:
          (json['floatingProfitLossRate'] as num?)?.toDouble(),
      marketValue: (json['marketValue'] as num?)?.toDouble(),
      restNum: (json['restNum'] as num?)?.toDouble(),
      takeProfitValue: (json['takeProfitValue'] as num?)?.toDouble(),
      stopLossValue: (json['stopLossValue'] as num?)?.toDouble(),
      appendMargin: (json['appendMargin'] as num?)?.toDouble(),
      marginRatio: (json['marginRatio'] as num?)?.toDouble(),
      warningLine: (json['warningLine'] as num?)?.toDouble(),
      distanceWarningLine: (json['distanceWarningLine'] as num?)?.toDouble(),
      closeLine: (json['closeLine'] as num?)?.toDouble(),
      distanceCloseLine: (json['distanceCloseLine'] as num?)?.toDouble(),
      feeAmount: (json['feeAmount'] as num?)?.toDouble(),
      positionDays: (json['positionDays'] as num?)?.toInt(),
      positionTotalNum: (json['positionTotalNum'] as num?)?.toDouble(),
      securityType: json['securityType'] as String?,
      createTime: json['createTime'] as String?,
      stockPrice: (json['stockPrice'] as num?)?.toDouble(),
      contractAccountId: (json['contractAccountId'] as num?)?.toInt(),
      periodType: (json['periodType'] as num?)?.toInt(),
      multiple: (json['multiple'] as num?)?.toInt(),
      winAmount: (json['winAmount'] as num?)?.toDouble(),
      tradeFee: (json['tradeFee'] as num?)?.toDouble(),
      contractId: (json['contractId'] as num?)?.toInt(),
      contractType: (json['contractType'] as num?)?.toInt(),
      chargeDetailList: (json['chargeDetailList'] as List<dynamic>?)
          ?.map((e) => ChargeDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OrderRecordImplToJson(_$OrderRecordImpl instance) =>
    <String, dynamic>{
      'cancelTime': instance.cancelTime,
      'currency': instance.currency,
      'dealNum': instance.dealNum,
      'dealPrice': instance.dealPrice,
      'dealTime': instance.dealTime,
      'direction': instance.direction,
      'id': instance.id,
      'market': instance.market,
      'priceType': instance.priceType,
      'status': instance.status,
      'symbol': instance.symbol,
      'symbolName': instance.symbolName,
      'tradeNum': instance.tradeNum,
      'tradePrice': instance.tradePrice,
      'tradeTime': instance.tradeTime,
      'tradeType': instance.tradeType,
      'transactionAmount': instance.transactionAmount,
      'type': instance.type,
      'availableMargin': instance.availableMargin,
      'marginAmount': instance.marginAmount,
      'buyAvgPrice': instance.buyAvgPrice,
      'buyTotalNum': instance.buyTotalNum,
      'costPrice': instance.costPrice,
      'disableNum': instance.disableNum,
      'floatingProfitLoss': instance.floatingProfitLoss,
      'floatingProfitLossRate': instance.floatingProfitLossRate,
      'marketValue': instance.marketValue,
      'restNum': instance.restNum,
      'takeProfitValue': instance.takeProfitValue,
      'stopLossValue': instance.stopLossValue,
      'appendMargin': instance.appendMargin,
      'marginRatio': instance.marginRatio,
      'warningLine': instance.warningLine,
      'distanceWarningLine': instance.distanceWarningLine,
      'closeLine': instance.closeLine,
      'distanceCloseLine': instance.distanceCloseLine,
      'feeAmount': instance.feeAmount,
      'positionDays': instance.positionDays,
      'positionTotalNum': instance.positionTotalNum,
      'securityType': instance.securityType,
      'createTime': instance.createTime,
      'stockPrice': instance.stockPrice,
      'contractAccountId': instance.contractAccountId,
      'periodType': instance.periodType,
      'multiple': instance.multiple,
      'winAmount': instance.winAmount,
      'tradeFee': instance.tradeFee,
      'contractId': instance.contractId,
      'contractType': instance.contractType,
      'chargeDetailList': instance.chargeDetailList,
    };

_$ChargeDetailImpl _$$ChargeDetailImplFromJson(Map<String, dynamic> json) =>
    _$ChargeDetailImpl(
      fee: (json['fee'] as num).toDouble(),
      feeName: json['feeName'] as String,
    );

Map<String, dynamic> _$$ChargeDetailImplToJson(_$ChargeDetailImpl instance) =>
    <String, dynamic>{
      'fee': instance.fee,
      'feeName': instance.feeName,
    };
