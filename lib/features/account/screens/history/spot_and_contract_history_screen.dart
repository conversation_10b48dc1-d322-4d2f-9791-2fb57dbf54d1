import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/market_table/market_table_row1.dart';
import '../../../../shared/widgets/pagination/common_refresher.dart';
import '../../../market/widgets/market_table_header.dart';
import '../../domain/models/order/order_response.dart';
import '../../logic/account/account_cubit.dart';
import '../../widgets/account_market_table.dart';
import '../../widgets/table_empty.dart';

/// This screen shows the history of the contract and spot orders.
class SpotAndContractHistoryScreen extends StatefulWidget {
  final ContractSummaryData? contract;
  const SpotAndContractHistoryScreen({super.key, this.contract});

  @override
  State<SpotAndContractHistoryScreen> createState() => _SpotAndContractHistoryScreenState();
}

class _SpotAndContractHistoryScreenState extends State<SpotAndContractHistoryScreen> {
  @override
  void initState() {
    super.initState();
    _init();
  }

  void _init() {
    final accountCubit = context.read<AccountCubit>();
    final assetId = getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString();
    accountCubit.resetContractHistoryType();
    accountCubit.getContractHistoryTransaction(
      contractId: widget.contract?.id,
      commentAssetId: assetId,
    );
    accountCubit.getContractHistoryCommission(
      contractId: widget.contract?.id,
      commentAssetId: assetId,
    );
  }

  @override
  void dispose() {
    context.read<AccountCubit>().resetContractHistoryType();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('transactionHistory'.tr()),
        surfaceTintColor: Colors.transparent,
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.gw,
        ),
        child: BlocSelector<AccountCubit, AccountState, ContractHistoryType>(
          selector: (state) => state.selectedContractHistoryType,
          builder: (context, selectedType) {
            return Column(
              children: [
                TableTabsSection(),
                10.verticalSpace,
                Expanded(
                  child: selectedType == ContractHistoryType.transaction
                      ? TableContentSection1(contract: widget.contract)
                      : CommissionHistoryTable(contract: widget.contract),
                ),
                // 10.verticalSpace,
              ],
            );
          },
        ),
      ),
    );
  }
}

class TableContentSection1 extends StatelessWidget {
  final ContractSummaryData? contract;
  const TableContentSection1({super.key, this.contract});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TableHeaderSection(),
          Expanded(child: TableBodySection(contract: contract)),
        ],
      ),
    );
  }
}

class TableHeaderSection extends StatelessWidget {
  const TableHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    final headerTitles = [
      'name_code'.tr(),
      'price_quantity'.tr(),
      'transaction_amount'.tr(),
      'direction_time'.tr(),
    ];

    final flexValues = [3, 2, 2, 2];

    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.gw,
                vertical: 12.gh,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  for (var i = 0; i < headerTitles.length; i++) ...[
                    if (i > 0) 5.horizontalSpace,
                    Expanded(
                      flex: flexValues[i],
                      child: Text(
                        headerTitles[i],
                        textAlign: i == 0 ? TextAlign.left : TextAlign.center,
                        style: context.textTheme.regular.fs12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Divider(
              color: context.theme.dividerColor,
              height: 1,
            ),
          ],
        );
      },
    );
  }
}

class TableBodySection extends StatelessWidget {
  final ContractSummaryData? contract;
  TableBodySection({super.key, this.contract});
  final RefreshController _refreshController = RefreshController();

  void _onRefresh({required BuildContext context}) {
    final assetId = getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString();
    final accountCubit = context.read<AccountCubit>();

    accountCubit.getContractHistoryTransaction(
      contractId: contract?.id,
      commentAssetId: assetId,
    );

    accountCubit.getContractHistoryCommission(
      contractId: contract?.id,
      commentAssetId: assetId,
    );

    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading({required BuildContext context, OrderData? records}) {
    if (records?.records?.length == records?.total) {
      _refreshController.loadNoData();
      return;
    }
    context.read<AccountCubit>().getContractHistoryTransaction(
          isLoadMore: true,
          contractId: contract?.id,
          commentAssetId: getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString(),
        );
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        final status = state.contractHistoryTransactionFetchStatus;
        final items = state.contractHistoryTransaction?.records;

        if (status == DataStatus.loading) {
          return const TableLoadingState();
        }

        // Always wrap with CommonRefresher, even when empty
        return SizedBox(
          height: .75.gsh,
          child: CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: items != null && items.isNotEmpty, // Only enable pull up when we have data
            onRefresh: () => _onRefresh(context: context),
            onLoading: () => _onLoading(context: context, records: state.contractHistoryTransaction),
            child: items == null || items.isEmpty
                ? const CustomScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    slivers: [
                      SliverFillRemaining(
                        child: TableEmptyWidget(),
                      ),
                    ],
                  )
                : ListView.separated(
                    physics: const AlwaysScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemCount: items.length,
                    separatorBuilder: (_, __) => Divider(
                      color: context.theme.dividerColor,
                      height: 1,
                    ),
                    itemBuilder: (_, index) => TradeDetailsRow(data: items[index], contract: contract),
                  ),
          ),
        );
      },
    );
  }
}

class TableTabsSection extends StatelessWidget {
  const TableTabsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return AnimationLimiter(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: _buildTabHeaders(context, state.selectedContractHistoryType),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildTabHeaders(BuildContext context, ContractHistoryType? selectedTab) {
    final headers = ContractHistoryType.values.map((type) => type.translationKey).toList();
    return headers.asMap().entries.expand((entry) {
      final index = entry.key;
      final header = entry.value;

      return [
        if (index > 0) 15.horizontalSpace,
        MarketTableHeader(
          title: header.tr(),
          isSelected: selectedTab == ContractHistoryType.values[index],
          onTap: () => context.read<AccountCubit>().updateContractHistoryType(ContractHistoryType.values[index]),
        ),
      ];
    }).toList();
  }
}

class CommissionHistoryTable extends StatelessWidget {
  final ContractSummaryData? contract;
  const CommissionHistoryTable({super.key, this.contract});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CommissionTableHeaderSection(),
          Expanded(child: CommissionTableBodySection(contract: contract)),
        ],
      ),
    );
  }
}

class CommissionTableHeaderSection extends StatelessWidget {
  const CommissionTableHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    final headerTitles = [
      'name_code'.tr(),
      'order_price'.tr(),
      'filled_total'.tr(),
      'direction_status'.tr(),
    ];

    final flexValues = [3, 2, 2, 2];

    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.gw,
                vertical: 12.gh,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  for (var i = 0; i < headerTitles.length; i++) ...[
                    if (i > 0) 5.horizontalSpace,
                    Expanded(
                      flex: flexValues[i],
                      child: Text(
                        headerTitles[i],
                        textAlign: i == 0 ? TextAlign.left : TextAlign.center,
                        style: context.textTheme.regular.fs12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Divider(
              color: context.theme.dividerColor,
              height: 1,
            ),
          ],
        );
      },
    );
  }
}

class CommissionTableBodySection extends StatelessWidget {
  final ContractSummaryData? contract;
  CommissionTableBodySection({super.key, this.contract});
  final RefreshController _refreshController = RefreshController();

  void _onRefresh({required BuildContext context}) {
    final accountCubit = context.read<AccountCubit>();
    final assetId = getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString();

    accountCubit.getContractHistoryCommission(
      contractId: contract?.id,
      commentAssetId: assetId,
    );

    accountCubit.getContractHistoryTransaction(
      contractId: contract?.id,
      commentAssetId: assetId,
    );

    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading({required BuildContext context, OrderData? records}) {
    if (records?.records?.length == records?.total) {
      _refreshController.loadNoData();
      return;
    }
    context.read<AccountCubit>().getContractHistoryCommission(
          isLoadMore: true,
          contractId: contract?.id,
          commentAssetId: getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString(),
        );
    _refreshController.loadComplete(); // Added this line to complete loading
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        final status = state.contractHistoryCommissionFetchStatus;
        final items = state.contractHistoryCommission?.records;

        if (status == DataStatus.loading) {
          return const TableLoadingState();
        }

        // Always wrap with CommonRefresher, even when empty
        return SizedBox(
          height: .75.gsh,
          child: CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: items != null && items.isNotEmpty, // Only enable pull up when we have data
            onRefresh: () => _onRefresh(context: context),
            onLoading: () => _onLoading(context: context, records: state.contractHistoryCommission),
            child: items == null || items.isEmpty
                ? const CustomScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    slivers: [
                      SliverFillRemaining(
                        child: TableEmptyWidget(),
                      ),
                    ],
                  )
                : ListView.separated(
                    physics: const AlwaysScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemCount: items.length,
                    separatorBuilder: (_, __) => Divider(
                      color: context.theme.dividerColor,
                      height: 1,
                    ),
                    itemBuilder: (_, index) => EntrustmentRow(
                      data: items[index],
                      showRevokeButton: false,
                    ),
                  ),
          ),
        );
      },
    );
  }
}
