import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_screen.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class WithdrawMainScreen extends StatefulWidget {
  const WithdrawMainScreen({super.key});

  @override
  State<WithdrawMainScreen> createState() => _WithdrawMainScreenState();
}

class _WithdrawMainScreenState extends State<WithdrawMainScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('withdrawal'.tr()),
        actions: [
          IconButton(
            icon: Icon(LucideIcons.notepad_text),
            onPressed: () {
              Navigator.pushNamed(context, routeWithdrawRecords);
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: context.theme.primaryColor,
          unselectedLabelColor: context.colorTheme.textRegular,
          indicatorColor: context.theme.primaryColor,
          indicatorWeight: 3,
          labelStyle: FontPalette.bold14,
          unselectedLabelStyle: context.textTheme.regular,
          dividerColor: Colors.transparent,
          tabs: [
            Tab(text: 'bank'.tr()),
            Tab(text: 'third'.tr()),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          MultiBlocProvider(
            providers: [
              BlocProvider<WithdrawalCubit>.value(value: context.read<WithdrawalCubit>()),
              BlocProvider<UserBankListCubit>.value(value: context.read<UserBankListCubit>()),
              BlocProvider<BankListCubit>.value(value: context.read<BankListCubit>()),
            ],
            child: const WithdrawScreen(
              showAppBar: false,
            ),
          ),
          ThirdPartyChannelScreen(showAppBar: false, transactionType: ThirdPartyTransactionType.withdraw),
        ],
      ),
    );
  }
}
