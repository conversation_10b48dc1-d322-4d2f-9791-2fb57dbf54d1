import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';

import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/screens/deposit_screen.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class DepositMainScreen extends StatefulWidget {
  const DepositMainScreen({super.key, this.depositType = DepositType.bank});
  final DepositType depositType;

  @override
  State<DepositMainScreen> createState() => _DepositMainScreenState();
}

class _DepositMainScreenState extends State<DepositMainScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: widget.depositType.index);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('deposit'.tr()),
        actions: [
          IconButton(
            icon: Icon(LucideIcons.notepad_text),
            onPressed: () {
              Navigator.pushNamed(context, routePayOrder);
            },
          ),
          // IconButton(
          //   icon: Icon(LucideIcons.notepad_text_dashed),
          //   onPressed: () {
          //     Navigator.pushNamed(context, routeDepositRecords);
          //   },
          // ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: context.theme.primaryColor,
          unselectedLabelColor: context.colorTheme.textRegular,
          indicatorColor: context.theme.primaryColor,
          indicatorWeight: 3,
          labelStyle: FontPalette.bold14,
          unselectedLabelStyle: context.textTheme.regular,
          dividerColor: Colors.transparent,
          tabs: [
            Tab(text: 'bankCard'.tr()),
            Tab(text: 'thirdPartyDeposit'.tr()),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // First tab - Bank Deposit
          MultiBlocProvider(
            providers: [
              BlocProvider<DepositCubit>.value(value: context.read<DepositCubit>()),
              BlocProvider<UserBankListCubit>.value(value: context.read<UserBankListCubit>()),
              BlocProvider<BankListCubit>.value(value: context.read<BankListCubit>()),
            ],
            child: const DepositScreen(),
          ),
          // Second tab - Third Party Channel
          BlocProvider<ThirdPartyChannelCubit>.value(
            value: getIt<ThirdPartyChannelCubit>(),
            child: ThirdPartyChannelScreen(showAppBar: false),
          ),
        ],
      ),
    );
  }
}
