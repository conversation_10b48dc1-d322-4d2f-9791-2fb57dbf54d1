import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class DistFlowChart extends StatelessWidget {
  final DistFlowData data;
  const DistFlowChart({super.key, required this.data});

  String _formatAxisLabel(double value, String locale) {
    // Handle both positive and negative values
    final isNegative = value < 0;
    final absValue = value.abs() * 1000000; // Convert back to original scale
    final formattedValue = formatLargeNumber(absValue, locale);
    return isNegative ? '-$formattedValue' : formattedValue;
  }

  @override
  Widget build(BuildContext context) {
    final spots = _generateSpots();
    // Calculate min and max Y values for proper chart scaling
    final yValues = spots.map((spot) => spot.y).toList();
    final minY = yValues.isEmpty ? 0.0 : yValues.reduce((a, b) => a < b ? a : b);
    final maxY = yValues.isEmpty ? 0.0 : yValues.reduce((a, b) => a > b ? a : b);
    // Add padding to the min/max values for better visualization
    final yPadding = (maxY - minY).abs() * 0.1;

    return Column(
      children: [
        Row(
          children: [
            Text(
              'fundTrend'.tr(),
              style: SecFontPalette.bold14.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          ],
        ),
        AspectRatio(
          aspectRatio: 1.8,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: LineChart(
              LineChartData(
                minY: minY - yPadding,
                maxY: maxY + yPadding,
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 60,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          _formatAxisLabel(value, context.locale.languageCode),
                          style: SecFontPalette.normal12.copyWith(
                            color: context.colorTheme.textRegular,
                          ),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 5),
                          child: Text(
                            _formatTime(value, "CN"),
                            style: FontPalette.bold10.copyWith(
                              color: context.colorTheme.textRegular,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                gridData: FlGridData(
                  show: true,
                  drawHorizontalLine: true,
                  drawVerticalLine: false,
                  getDrawingHorizontalLine: (value) {
                    // Make the zero line more prominent
                    if (value == 0) {
                      return FlLine(
                        color: context.colorTheme.textRegular,
                        strokeWidth: 1,
                      );
                    }
                    return FlLine(
                      color: context.colorTheme.textRegular.withValues(alpha: 0.2),
                      strokeWidth: 0.5,
                    );
                  },
                ),
                lineBarsData: [
                  LineChartBarData(
                    spots: spots,
                    isCurved: true,
                    color: Colors.blueAccent,
                    barWidth: 1,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.blueAccent.withValues(alpha: 0.05),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  enabled: true,
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipColor: (spot) => context.theme.scaffoldBackgroundColor,
                    getTooltipItems: (touchedSpots) {
                      return touchedSpots.map((spot) {
                        return LineTooltipItem(
                          _formatAxisLabel(spot.y, context.locale.languageCode),
                          SecFontPalette.normal12.copyWith(
                            color: context.colorTheme.textPrimary,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<FlSpot> _generateSpots() {
    if (data.flow == null || data.flow!.isEmpty) {
      return [];
    }

    final flows = data.flow!.where((flow) => flow.date != null && flow.chg != null).toList();

    if (flows.isEmpty) {
      return [];
    }

    final minTimestamp = flows.map((f) => f.date!).reduce((a, b) => a < b ? a : b).toDouble();

    final spots = flows.map((flow) {
      final x = flow.date!.toDouble() - minTimestamp;
      final y = flow.chg! / 1000000; // Convert to millions for better readability
      return FlSpot(x, y);
    }).toList();

    return spots.isEmpty ? [] : spots;
  }

  String _formatTime(double xValue, String countryCode) {
    if (!countryTimeZones.containsKey(countryCode)) return '';
    final timeZone = countryTimeZones[countryCode]!;
    final flows = data.flow!.where((flow) => flow.date != null).toList();
    if (flows.isEmpty) return '';

    final minTimestamp = flows.first.date!;
    final adjustedTimestamp = minTimestamp + xValue.toInt();
    return TimeZoneHelper.formatTimeInZone(adjustedTimestamp, timeZone);
  }
}
