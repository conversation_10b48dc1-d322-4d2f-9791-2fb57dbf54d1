import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/confirm_dialog_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/position_dropdown.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

class BuySection extends StatefulWidget {
  /// A widget that displays the buy section of the trading form with comprehensive trading functionality.
  ///
  /// This widget handles different trading modes and operations:
  /// - A-Shares Trading: Simple buy operations with fee calculations
  /// - Index Trading: Complex operations including long/short positions
  /// - Position Management: Tracks and manages trading positions
  ///
  /// Key Features:
  /// - Dynamic button labels based on market type (buy, open long, open short)
  /// - Automatic fee calculations for different trading types
  /// - Position tracking for index trading
  /// - Multi-currency support with exchange rate conversions
  /// - Real-time amount calculations and updates
  ///
  /// State Management:
  /// - Uses BlocSelector for efficient state updates
  /// - Manages trading state through TradingCubit
  /// - Handles position state via AccountCubit
  /// - Integrates with IndexTradeCubit for index-specific operations
  ///
  /// UI Components:
  /// - Position selection dropdown for short positions (Index Trading only)
  /// - Amount display rows for fees, order amounts, and totals
  /// - Currency conversion display for non-A-Share markets
  /// - Confirmation dialog for trade execution
  const BuySection({super.key, required this.buyType});

  final BuyType buyType;

  @override
  State<BuySection> createState() => _BuySectionState();
}

class _BuySectionState extends State<BuySection> {
  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        TradingCubit,
        TradingState,
        ({
          StockInfoData? stockInfoData,
          double limit,
          double quantity,
          double accountBalance,
          TradeDirection tradeDirection,
          List<CalculateConfig> chargeList,
          double amount,
          OrderRecord? selectedPosition,
          ContractSummaryData? contract,
          bool isIndexTrading,
          int tradeUnit,
        })>(
      selector: (state) => (
        stockInfoData: state.stockInfoConstant,
        limit: state.limit,
        quantity: state.quantity,
        accountBalance: state.accountBalance,
        tradeDirection: state.tradeDirection,
        chargeList: state.tradeDirection.value == 1 ? state.calculateConfigBuy : state.calculateConfigSell,
        amount: state.amount,
        selectedPosition: state.selectedPositionOpenShort,
        contract: state.contract,
        isIndexTrading: state.isIndexTrading,
        tradeUnit: state.indexStockData?.tradeUnit ?? 0,
      ),
      builder: (context, state) {
        final cubit = context.read<TradingCubit>();
        double amount = 0;
        bool enabled = cubit.isBuyingEnabled();
        String buyLabel = '';
        String availableLabel = '';
        String lotLabel = '';
        double available = cubit.getQuantityByFraction(OrderFraction.full);
        int tradeType = 1;
        final dealPrice = cubit.dealPrice;
        final marketType = getMainMarketType(state.stockInfoData?.market ?? '');
        final order = state.quantity * dealPrice;
        amount = order;
        double fee = cubit.calculateFee(amount);
        double totalAmount = 0;
        final positions =
            context.select<AccountCubit, List<OrderRecord>?>((state) => state.state.currentPositions?.records) ?? [];
        cubit.getAvailableQuantity(positions, true);
        ({int long, int short}) getAvailableToClose(List<OrderRecord>? positions) {
          if (positions == null || positions.isEmpty) return (long: 0, short: 0);
          // Filter positions based on tradeType
          final longPositions = positions.where((p) => p.tradeType == 1).toList();
          final shortPositions = positions.where((p) => p.tradeType == 2).toList();
          final longSum = longPositions.fold(0, (sum, p) => sum + (p.restNum ?? 0).toInt());
          final shortSum = shortPositions.fold(0, (sum, p) => sum + (p.restNum ?? 0).toInt());
          return (long: longSum, short: shortSum);
        }

        if (state.isIndexTrading) {
          lotLabel = 'lotForStockIndex'.tr();
        } else {
          lotLabel = 'lotForSecurities'.tr();
        }
        if (marketType == MainMarketType.cnShares && !state.isIndexTrading) {
          buyLabel = 'buy'.tr();
          availableLabel = 'availableToBuy'.tr();
          totalAmount = amount + fee;
          // available = state.accountBalance.toInt();
        } else {
          if (state.tradeDirection == TradeDirection.buy) {
            buyLabel = 'openLong'.tr();
            availableLabel = 'availableToOpen'.tr();
            totalAmount = amount + fee;
            // if (!isIndexTrading) available = getAvailableToClose(positions).long.toDouble();
          } else {
            buyLabel = 'sellLong'.tr();
            available = getAvailableToClose(positions).short.toDouble();
            availableLabel = 'availableToClose'.tr();
            totalAmount = amount - fee;
            tradeType = 2;
            if (state.selectedPosition != null) {
              available = state.selectedPosition?.restNum ?? 0;
              totalAmount = amount - fee;
            }
          }
        }
        enabled = available > 0;
        final exchangeRate =
            context.watch<ExchangeRateCubit>().getCurrencyRateConfig(state.stockInfoData?.currency ?? '');
        cubit.setExchangeRate(state.contract != null ? null : exchangeRate);
        return Column(
          spacing: 8,
          children: [
            if (state.isIndexTrading && state.tradeDirection == TradeDirection.sell)
              PositionDropdown(
                positions: positions,
                tradeType: tradeType,
                selectedPosition: state.selectedPosition,
                onPositionSelected: (value) {
                  if (value != null) {
                    cubit.setSelectedPositionOpenShort(value);
                  }
                },
              ),
            AmountRow(
              title: availableLabel,
              amount: available,
              suffix: lotLabel,
              fontSize: 13.gr,
              fractionDigits: state.isIndexTrading ? 1 : 0,
            ),
            AmountRow(
              title: 'service_charge'.tr(),
              amount: fee,
              fontSize: 13.gr,
              currency: state.stockInfoData?.currency,
            ),
            AmountRow(
              title: 'orderAmount'.tr(),
              amount: amount,
              fontSize: 13.gr,
              currency: state.stockInfoData?.currency,
            ),
            AmountRow(
              title: 'totalPrice'.tr(),
              amount: totalAmount,
              fontSize: 13.gr,
              currency: state.stockInfoData?.currency,
              showTotalToolTip: true,
            ),
            if (marketType != MainMarketType.cnShares && state.contract == null)
              Row(
                children: [
                  Spacer(),
                  AnimatedFlipCounter(
                    prefix: '≈ ',
                    duration: const Duration(milliseconds: 500),
                    suffix: ' ${exchangeRate.currencyBase}',
                    thousandSeparator: ',',
                    fractionDigits: 2,
                    textStyle: FontPalette.extraBold14.copyWith(
                      color: context.theme.primaryColor,
                      fontFamily: 'Akzidenz-Grotesk',
                      height: 1,
                      fontSize: 13.gr,
                    ),
                    value: totalAmount / exchangeRate.rate,
                  )
                ],
              ),
            BlocSelector<TradingCubit, TradingState, bool>(
              selector: (state) => state.createBuyOrderStatus == DataStatus.loading,
              builder: (context, isLoading) {
                return CommonButton(
                  height: 30.gh,
                  showLoading: isLoading,
                  enable: enabled,
                  onPressed: enabled
                      ? () {
                          if (cubit.state.tradingType.notNullNorEmpty) {
                            showDialog(
                              context: context,
                              builder: (_) {
                                return BlocProvider.value(
                                  value: cubit,
                                  child: ConfirmDialogWidget(
                                    tradeType: tradeType,
                                    directionLabel: buyLabel,
                                    exchangeRate: exchangeRate,
                                    marketType: marketType,
                                    calculateConfig: state.chargeList,
                                    amount: amount,
                                    totalAmount: totalAmount,
                                    totalAmountUnified: totalAmount / exchangeRate.rate,
                                    selectedPosition: state.selectedPosition,
                                    isBuySection: true,
                                  ),
                                );
                              },
                            );
                          } else {
                            GPEasyLoading.showToast('selectTradingType'.tr());
                          }
                        }
                      : null,
                  title: buyLabel,
                  style: CommonButtonStyle.stockGreen,
                  fontSize: 13.gr,
                );
              },
            ),
            SizedBox(height: 8),
          ],
        );
      },
    );
  }
}
