import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

class AccountBalance extends StatefulWidget {
  /// A widget that displays the account balance with an animated counter.
  ///
  /// This widget listens to both [TradingCubit] and [AccountCubit] states to display
  /// either the contract balance or usable cash balance. It automatically updates when:
  /// - The account's usable cash changes
  /// - A contract is selected/deselected
  /// - The trading type changes (index vs regular trading)
  const AccountBalance({super.key});

  @override
  State<AccountBalance> createState() => _AccountBalanceState();
}

class _AccountBalanceState extends State<AccountBalance> {

  @override
  void initState() {
    super.initState();
    _setBalance(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        TradingCubit,
        TradingState,
        ({
          int? contractId,
          double accountBalance,
          bool isIndexTrading,
          String? contractCurrency,
          ContractSummaryData? contract
        })>(
      selector: (state) => (
        contractId: state.contractId,
        accountBalance: state.accountBalance,
        isIndexTrading: state.isIndexTrading,
        contractCurrency: state.contract?.currency,
        contract: state.contract
      ),
      builder: (context, state) {
        return MultiBlocListener(
          listeners: [
            BlocListener<TradingCubit, TradingState>(
              listenWhen: (previous, current) =>
                  previous.contractId != null &&
                  previous.stockInfoConstant == null &&
                  current.stockInfoConstant != null,
              listener: (context, state) =>
                  _setBalance(context, contractId: state.contractId, contract: state.contract),
            ),
            BlocListener<AccountInfoCubit, AccountInfoState>(
              listenWhen: (previous, current) => previous.accountInfo?.usableCash != current.accountInfo?.usableCash,
              listener: (context, state) => _setBalance(context),
            ),
          ],
          child: BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
            selector: (state) => (accountInfoData: state.accountInfo),
            builder: (context, accountState) {
              return _buildBalanceDisplay(
                context,
                contractCurrency: state.contractCurrency,
                balance: state.contract == null
                    ? accountState.accountInfoData?.usableCash ?? 0
                    : state.contract?.useAmount ?? 0,
              );
            },
          ),
        );
      },
    );
  }

  /// Builds the balance display widget with proper styling and animation.
  ///
  /// [contractCurrency] The currency to display (defaults to CNY if null).
  /// [balance] The current balance value to display.
  Widget _buildBalanceDisplay(
    BuildContext context, {
    String? contractCurrency,
    required double balance,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'balance'.tr(),
              style: FontPalette.normal13.copyWith(color: ColorPalette.subTitleColor),
            ),
            AnimatedFlipCounter(
              thousandSeparator: ',',
              fractionDigits: 2,
              suffix: ' ${contractCurrency ?? 'CNY'}',
              textStyle: FontPalette.extraBold14.copyWith(
                color: context.appTheme.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
                height: 1,
              ),
              value: balance,
            )
          ],
        ),
      ],
    );
  }

  /// Sets the account balance based on the provided contract ID and contract data.
  ///
  /// If [contractId] is null, the balance is set to the usable cash from the account info.
  /// If [contractId] is present, the balance is set to the use amount from the contract.
  ///
  /// [contractId] The ID of the contract to use for balance calculation.
  /// [contract] The contract data to use for balance calculation.
  void _setBalance(
    BuildContext context, {
    int? contractId,
    ContractSummaryData? contract,
  }) {
    final balance = contractId == null
        ? context.read<AccountInfoCubit>().state.accountInfo?.usableCash ?? 0
        : contract?.useAmount ?? 0;
    if (balance > 0) {
      context.read<TradingCubit>()
        ..setAccountBalance(balance)
        ..setOrderFraction(orderFraction: OrderFraction.full);
    }
  }
}
