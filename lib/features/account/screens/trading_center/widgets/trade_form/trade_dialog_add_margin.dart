import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_state_v2.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

/// 追加保证金
class TradeAddMarginDialog {
  final BuildContext context;
  final FTradeAcctOrderRecords data;
  final TextEditingController textController = TextEditingController();
  final Function(double amount) onPressedSubmit;

  TradeAddMarginDialog(
    this.context, {
    required this.data,
    required this.onPressedSubmit,
  });

  Future<Map<String, dynamic>?> show() async {
    final textController = TextEditingController();
    final cubit = context.read<AccountScreenCubitV2>();
    /// 用于更新可用保证金
    cubit.updateCurrentSelectSpotOrder(data);
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => BlocSelector<UserCubit, UserState, double>(
        selector: (state) => state.accountInfo?.assetAmount ?? 0,
        builder: (context, assetAmount) {
          return BlocSelector<AccountScreenCubitV2, AccountScreenStateV2,
              ({double marginAmount, double availableMargin})>(
            selector: (state) => (
            marginAmount: state.currentSelectSpotOrder?.marginAmount ?? 0,
            availableMargin: state.currentSelectSpotOrder?.availableMargin ?? 0,
            ),
            builder: (context, state) {
              return TradeAddMarginDialogContent(
                positionMarginAmount: state.marginAmount,
                availableMarginAmount: state.availableMargin,
                availableBalance: assetAmount,
                textController: textController,
                onPressedSubmit: onPressedSubmit,
              );
            },
          );
        },
      ),
    );
    /// 用完释放
    cubit.updateCurrentSelectSpotOrder(null);
    return result;
  }
}

class TradeAddMarginDialogContent extends StatefulWidget {
  final double positionMarginAmount;
  final double availableMarginAmount;
  final double availableBalance;
  final TextEditingController textController;
  final Function(double amount) onPressedSubmit;

  const TradeAddMarginDialogContent({
    super.key,
    required this.positionMarginAmount,
    required this.availableMarginAmount,
    required this.availableBalance,
    required this.textController,
    required this.onPressedSubmit,
  });

  @override
  State<TradeAddMarginDialogContent> createState() => _TradeAddMarginDialogContentState();
}

class _TradeAddMarginDialogContentState extends State<TradeAddMarginDialogContent> {
  bool isAgreeProtocol = false;
  bool isSubmitBtnLoading = false;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              width: 347.gw,
              padding: EdgeInsets.fromLTRB(15.gw, 20.gw, 15.gw, 22.gw),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.gr),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'marginCall'.tr(),
                        style: FontPalette.medium14.copyWith(color: ColorPalette.titleColor),
                      ),
                    ],
                  ),
                  11.verticalSpace,
                  AmountRow(
                    title: 'position_margin'.tr(), // 持仓保证金
                    amount: widget.positionMarginAmount,
                  ),
                  8.verticalSpace,
                  AmountRow(
                    title: 'available_margin'.tr(), // 可用保证金
                    amount: widget.availableMarginAmount,
                  ),
                  8.verticalSpace,
                  AmountRow(
                    title: 'available_balance'.tr(), // 可用金额
                    amount: widget.availableBalance,
                  ),
                  25.verticalSpace,
                  Text(
                    'contract_deposit'.tr(),
                    style: FontPalette.medium16.copyWith(
                      color: context.appTheme.titleColor,
                    ),
                  ),
                  12.verticalSpace,
                  TextFieldWidget(
                    controller: widget.textController,
                    hintText: 'contract_deposit'.tr(),
                    textInputType: TextInputType.numberWithOptions(decimal: true),
                    hintStyle: FontPalette.medium16.copyWith(color: ColorPalette.titleColor),
                    fillColor: const Color(0xffECF0FF),
                    showBorderSide: false,
                  ),
                  12.verticalSpace,
                  _buildAgreementRow(context, context.appTheme),
                  25.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomMaterialButton(
                          buttonText: 'cancel'.tr(),
                          borderColor: Color(0xffD9D9D9),
                          color: Colors.white,
                          textColor: Colors.black,
                          padding: EdgeInsets.zero,
                          isOutLined: true,
                          fontSize: 16,
                          borderRadius: 8.gw,
                          onPressed: () async {
                            Navigator.of(context).pop();
                          },
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: CustomMaterialButton(
                          buttonText: 'confirm'.tr(),
                          borderColor: ColorPalette.primaryColor,
                          color: ColorPalette.primaryColor,
                          padding: EdgeInsets.zero,
                          fontSize: 16,
                          borderRadius: 8.gw,
                          isLoading: isSubmitBtnLoading,
                          isEnabled: isAgreeProtocol,
                          onPressed: () async {
                            FocusScope.of(context).unfocus();

                            final amount = double.tryParse(widget.textController.text) ?? 0;

                            if (amount != 0) {
                              setState(() {
                                isSubmitBtnLoading = true;
                              });
                              try {
                                await widget.onPressedSubmit.call(amount);
                              } finally {
                                if (mounted) {
                                  setState(() {
                                    isSubmitBtnLoading = false;
                                  });
                                }
                              }
                            } else {
                              GPEasyLoading.showToast('please_enter_margin'.tr()); // 请先设置'止盈/止损'价格
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void updateIsAgreeProtocol(bool value) {
    setState(() {
      isAgreeProtocol = value;
    });
  }

  Widget _buildAgreementRow(BuildContext context, MyColorScheme colorScheme) {
    return Row(
      children: [
        CustomRadioButton(
          isSelected: isAgreeProtocol,
          onChange: (value) => updateIsAgreeProtocol(value),
        ),
        9.horizontalSpace,
        Text.rich(
          TextSpan(
            text: '${'readAndAgree'.tr()} ',
            style: FontPalette.normal10.copyWith(
              color: colorScheme.titleColor,
            ),
            children: [
              TextSpan(
                text: 'marginAgreement'.tr(),
                style: FontPalette.normal10.copyWith(
                  color: colorScheme.primaryColor,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    showAppInfoBottomSheet(context);
                  },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
