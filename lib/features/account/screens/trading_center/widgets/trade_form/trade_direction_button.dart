import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TradeDirectionButton extends StatelessWidget {
  const TradeDirectionButton({
    super.key,
    required this.text,
    required this.color,
    required this.onPressed,
    required this.isSelected,
  });

  final String text;
  final Color color;
  final VoidCallback onPressed;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      height: 28.gh,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      elevation: 0,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      minWidth: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.gr)),
      color: isSelected ? color : context.theme.scaffoldBackgroundColor,
      onPressed: onPressed,
      child: Text(
        text,
        style: FontPalette.normal13.copyWith(
          color: isSelected ? context.theme.cardColor : context.colorTheme.textPrimary,
        ),
      ),
    );
  }
}
