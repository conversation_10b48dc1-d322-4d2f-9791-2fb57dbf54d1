import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class IndexRadioSection extends StatelessWidget {
  /// A widget that displays radio buttons for selecting between open trade and timed trading modes.
  ///
  /// This widget provides:
  /// - Toggle between open trade and timed trading modes
  /// - Automatic disabling of timed trading when timer is closed
  /// - Help tooltip with trading unit information
  /// - Integration with trading and index trade state management
  const IndexRadioSection({super.key});

  @override
  Widget build(BuildContext context) {
    // Select trading states from cubits
    final isTimedTrading = context.select((TradingCubit cubit) => cubit.state.isTimedTrading);
    final index = context.select((IndexTradeCubit cubit) {
      if (cubit.state.indexes.isEmpty) return null;
      return cubit.state.indexes[cubit.state.selectedIndex];
    });

    // Calculate trading parameters
    final tradeUnit = index?.tradeUnit ?? 1;
    final isTimedClosed = !(index?.isTimerClose ?? true);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRadioOption(
          context: context,
          value: false,
          isTimedTrading: isTimedTrading,
          isDisabled: false,
          label: 'openTrade'.tr(),
        ),
        Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 30.gw),
              child: _buildRadioOption(
                context: context,
                value: true,
                isTimedTrading: isTimedTrading,
                isDisabled: isTimedClosed,
                label: 'timed'.tr(),
              ),
            ),
            Positioned(
              bottom: 0,
              right: -10,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: Icon(
                  Icons.help_outline,
                  size: 14.gsp,
                  color: context.theme.primaryColor,
                ),
                onPressed: () => _showHelpModal(context, tradeUnit),
              ),
            ),
          ],
        ),
        20.horizontalSpace,
      ],
    );
  }

  /// Shows the help modal with trading unit information
  void _showHelpModal(BuildContext context, int tradeUnit) {
    showModalBottomSheet(
      context: context,
      showDragHandle: true,
      builder: (context) => Container(
        padding: EdgeInsets.only(left: 16.gh, right: 16.gw, bottom: 30.gh, top: 0.gh),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'indexTradingTip'.tr(args: [tradeUnit.toString()]),
              style: FontPalette.normal14.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a radio button with label for trading mode selection
  Widget _buildRadioOption({
    required BuildContext context,
    required bool value,
    required bool isTimedTrading,
    required bool isDisabled,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Radio(
          value: value,
          groupValue: isTimedTrading,
          onChanged: isDisabled ? null : (bool? newValue) => context.read<TradingCubit>().setTimedTrading(newValue!),
          activeColor: context.theme.primaryColor,
          fillColor: isDisabled
              ? WidgetStateProperty.all(ColorPalette.borderColor)
              : WidgetStateProperty.all(context.theme.primaryColor ),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
        ),
        GestureDetector(
          onTap: isDisabled ? null : () => context.read<TradingCubit>().setTimedTrading(value),
          child: Text(
            label,
            style: FontPalette.normal11.copyWith(
              color: isDisabled ? ColorPalette.borderColor : null,
            ),
          ),
        ),
      ],
    );
  }
}
