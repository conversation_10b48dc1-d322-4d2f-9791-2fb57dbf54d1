import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/entrustment_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/position_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_positions.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../shared/widgets/pagination/common_refresher.dart';
import '../../market/widgets/market_table_header.dart';
import '../domain/models/order/order_response.dart';
import '../logic/account/account_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// TradingDataTable is a widget that displays trading data in a table format.
/// It shows order details and current positions for a given market, security type, and symbol.
/// This is only for trading center screen.
class TradingDataTable extends StatelessWidget {
  const TradingDataTable({
    super.key,
    required this.market,
    required this.securityType,
    required this.symbol,
    required this.isIndexTrading,
  });
  final String market;
  final String securityType;
  final String symbol;
  final bool isIndexTrading;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TradingTableTabsSection(market: market, isIndexTrading: isIndexTrading),
        8.verticalSpace,
        TradingTableContentSection(
          market: market,
          securityType: securityType,
          symbol: symbol,
        ),
      ],
    );
  }
}

class TradingTableTabsSection extends StatelessWidget {
  const TradingTableTabsSection({super.key, required this.market, required this.isIndexTrading});
  final String market;
  final bool isIndexTrading;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: EdgeInsets.only(top: 16.gh),
            child: AnimationLimiter(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 300),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: widget,
                    ),
                  ),
                  children: _buildTabHeaders(context, state, market, isIndexTrading),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildTabHeaders(BuildContext context, AccountState state, String market, bool isIndexTrading) {
    final marketTypeList = isIndexTrading
        ? [AccountMarketType.currentPositions]
        : [AccountMarketType.orderDetails, AccountMarketType.currentPositions];
    final headers = marketTypeList.map((type) => type.translationKey2(market)).toList();
    return headers.asMap().entries.expand((entry) {
      final index = entry.key;
      final header = entry.value;
      final type = marketTypeList[index];
      final count = switch (type) {
        AccountMarketType.currentPositions => state.currentPositions?.total ?? 0,
        AccountMarketType.orderDetails => state.orderDetails?.total ?? 0,
        _ => 0,
      };

      return [
        if (index > 0) 15.horizontalSpace,
        MarketTableHeader(
          title: '${header.tr()} ($count)',
          isSelected: state.selectedTableTab == type,
          onTap: () => context.read<AccountCubit>().updateTableTab(type),
        ),
      ];
    }).toList();
  }
}

class TradingTableContentSection extends StatelessWidget {
  const TradingTableContentSection({
    super.key,
    required this.market,
    required this.securityType,
    required this.symbol,
  });
  final String market;
  final String securityType;
  final String symbol;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TradingTableHeaderSection(),
          TradingTableBodySection(
            market: market,
            securityType: securityType,
            symbol: symbol,
          ),
        ],
      ),
    );
  }
}

class TradingTableHeaderSection extends StatelessWidget {
  const TradingTableHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    final headerTitles3 = [
      '${'name'.tr()} | ${'code'.tr()}',
      'orderPrice'.tr(),
      '${'completed'.tr()} | ${'total'.tr()}',
      '${'direction'.tr()} | ${'status'.tr()}',
    ];

    final flexValues = [3, 2, 2, 2];

    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        final headerTitles = state.selectedTableTab == AccountMarketType.orderDetails ? headerTitles3 : [];

        return state.selectedTableTab != AccountMarketType.currentPositions
            ? Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.gw,
                      vertical: 12.gh,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        for (var i = 0; i < headerTitles.length; i++) ...[
                          if (i > 0) 5.horizontalSpace,
                          Expanded(
                              flex: flexValues[i],
                              child: Tooltip(
                                message: headerTitles[i],
                                child: Text(
                                  headerTitles[i],
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  textAlign: i == 0 ? TextAlign.left : TextAlign.center,
                                  style: FontPalette.normal12.copyWith(
                                    color: context.colorTheme.textRegular,
                                  ),
                                ),
                              )),
                        ],
                      ],
                    ),
                  ),
                  Divider(
                    color: context.theme.dividerColor,
                    height: 1,
                  ),
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }
}

class TradingTableBodySection extends StatelessWidget {
  TradingTableBodySection({
    super.key,
    required this.market,
    required this.securityType,
    required this.symbol,
  });
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final String market;
  final String securityType;
  final String symbol;
  void _onRefresh(
    BuildContext context, {
    required AccountMarketType type,
    required AccountState accountState,
  }) {
    context.read<AccountCubit>().getOrderList(
          type,
          symbol: symbol,
          market: market,
          securityType: securityType,
          contractId: context.read<TradingCubit>().state.contract?.id,
        );
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading({
    required BuildContext context,
    required RefreshController refreshController,
  }) {
    AccountMarketType type = context.read<AccountCubit>().state.selectedTableTab;
    OrderData? records = switch (type) {
      AccountMarketType.currentPositions => context.read<AccountCubit>().state.currentPositions,
      AccountMarketType.orderDetails => context.read<AccountCubit>().state.orderDetails,
      _ => null,
    };

    if (records == null || records.records == null) {
      refreshController.loadNoData();
      return;
    }

    final currentLength = records.records?.length ?? 0;
    final totalItems = records.total ?? 0;

    if (currentLength >= totalItems) {
      refreshController.loadNoData();
      return;
    }

    context.read<AccountCubit>().getOrderList(type, isLoadMore: true);
    refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TradingCubit, TradingState>(
      listenWhen: (previous, current) => previous.orderCancelStatus != current.orderCancelStatus,
      listener: (context, state) {
        if (state.orderCancelStatus == DataStatus.loading) {
          GPEasyLoading.showLoading(message: 'processingTrade'.tr());
        }
        if (state.orderCancelStatus == DataStatus.success || state.orderCancelStatus == DataStatus.failed) {
          GPEasyLoading.dismiss();
          if (state.orderCancelStatus == DataStatus.success) {
            GPEasyLoading.showSuccess(message: 'orderCancelSuccess'.tr());
            context.read<AccountCubit>().getOrderList(
                  AccountMarketType.currentPositions,
                  symbol: symbol,
                  market: market,
                  securityType: securityType,
                  contractId: state.contract?.id,
                );
            context.read<AccountCubit>().getOrderList(
                  AccountMarketType.orderDetails,
                  symbol: symbol,
                  market: market,
                  securityType: securityType,
                  contractId: state.contract?.id,
                );
          } else if (state.orderCancelStatus == DataStatus.failed) {
            GPEasyLoading.showToast(state.error);
          }
        }
      },
      child: BlocBuilder<AccountCubit, AccountState>(
        builder: (context, state) {
          final status = _getTableStatus(state);
          final items = _getTableItems(state);
          if (status == DataStatus.loading) {
            return const TableLoadingState();
          }
          if (items == null || (items.records?.isEmpty ?? true)) {
            return const TableEmptyWidget();
          }
          return SizedBox(
            height: .45.gsh,
            child: CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              enablePullUp: true,
              onRefresh: () => _onRefresh(context, type: state.selectedTableTab, accountState: state),
              onLoading: () => _onLoading(
                context: context,
                refreshController: _refreshController,
              ),
              child: ListView.separated(
                physics: const AlwaysScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: EdgeInsets.only(bottom: 16.gh),
                itemCount: items.records?.length ?? 0,
                separatorBuilder: (_, __) => Divider(
                  color: context.theme.dividerColor,
                  height: 1,
                ),
                itemBuilder: (_, index) => switch (state.selectedTableTab) {
                  AccountMarketType.currentPositions => PositionTableRow(data: items.records?[index] ?? OrderRecord()),
                  AccountMarketType.orderDetails => OrderTableRow(data: items.records?[index] ?? OrderRecord()),
                  _ => const SizedBox.shrink(),
                },
              ),
            ),
          );
        },
      ),
    );
  }

  DataStatus _getTableStatus(AccountState state) {
    return switch (state.selectedTableTab) {
      AccountMarketType.currentPositions => state.currentPositionsFetchStatus,
      AccountMarketType.orderDetails => state.orderDetailsFetchStatus,
      _ => DataStatus.idle,
    };
  }

  OrderData? _getTableItems(AccountState state) {
    return switch (state.selectedTableTab) {
      AccountMarketType.currentPositions => state.currentPositions,
      AccountMarketType.orderDetails => state.orderDetails,
      _ => null,
    };
  }
}

class PositionTableRow extends StatelessWidget {
  const PositionTableRow({super.key, required this.data});
  final OrderRecord data;

  @override
  Widget build(BuildContext context) {
    return PositionWidget(
      data: data,
      onTap: () {
        context
            .read<TradingCubit>()
            .getKlineDetailList(context.read<TradingCubit>().state.stockInfo?.instrument, KlineConstants.options[1]);
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (_) {
            return BlocProvider.value(
              value: context.read<TradingCubit>(),
              child: TradeBottomsheetPositions(data: data),
            );
          },
        );
      },
    );
  }
}

class OrderTableRow extends StatelessWidget {
  const OrderTableRow({
    super.key,
    required this.data,
  });

  final OrderRecord data;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context
            .read<TradingCubit>()
            .getKlineDetailList(context.read<TradingCubit>().state.stockInfo?.instrument, KlineConstants.options[0]);
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (_) {
            return BlocProvider.value(
              value: context.read<TradingCubit>(),
              child: TradeBottomsheetOrders(data: data),
            );
          },
        );
      },
      child: EntrustmentWidget(
        data: data,
        showRevokeButton: false,
      ),
    );
  }
}
