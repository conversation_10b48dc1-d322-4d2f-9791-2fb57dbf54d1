import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';

import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/widgets/flip_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class DetailHeader extends StatelessWidget {
  final String label;
  final num value;
  final bool showArrow;
  final SelectedExchangeCubit selectedExchangeCubit;
  const DetailHeader({
    super.key,
    required this.label,
    required this.value,
    required this.selectedExchangeCubit,
    this.showArrow = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(5.gr),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                label,
                style: FontPalette.semiBold14.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              8.horizontalSpace,
              FlipText(
                value.toDouble(),
                selectedExchangeCubit: selectedExchangeCubit,
                isCurrency: true,
                showCurrencyDropdown: true,
                dropdownIconColor: context.theme.primaryColor,
                style: FontPalette.semiBold14.copyWith(
                  fontFamily: 'Akzidenz-Grotesk',
                  color: context.theme.primaryColor,
                ),
              )
            ],
          ),
          if (showArrow) ...[
            8.horizontalSpace,
            Icon(
              Icons.arrow_forward_ios,
              size: 16.gr,
              color: context.colorTheme.textRegular,
            ),
          ],
        ],
      ),
    );
  }
}
