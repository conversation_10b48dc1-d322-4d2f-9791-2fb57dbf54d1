import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/market_table_header.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_list.dart';

import '../../market/watch_list/widgets/wishlist_data_table.dart';
import '../widgets/market_data_table.dart';

class StyleAHomeMarketTab extends StatefulWidget {
  const StyleAHomeMarketTab({super.key});

  @override
  State<StyleAHomeMarketTab> createState() => _StyleAHomeMarketTabState();
}

class _StyleAHomeMarketTabState extends State<StyleAHomeMarketTab> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0F354677),
            offset: Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPillTab(),
          16.verticalSpace,
          _buildTabContent(),
        ],
      ),
    );
  }

  Widget _buildPillTab() {
    final tabs = [
      (title: 'watchList', tab: 0),
      (title: 'stockIndex', tab: 1),
      (title: 'stocks', tab: 2),
      (title: 'marketTitle7', tab: 3),
    ];

    return AnimationLimiter(
      child: Row(
          children: AnimationConfiguration.toStaggeredList(
        duration: const Duration(milliseconds: 300),
        childAnimationBuilder: (widget) => SlideAnimation(
          horizontalOffset: 50.0,
          child: FadeInAnimation(
            child: widget,
          ),
        ),
        children: tabs
            .map(
              (tabInfo) => Padding(
                padding: EdgeInsets.only(right: 15.gr),
                child: MarketTableHeader(
                  title: tabInfo.title.tr(),
                  isSelected: _selectedTabIndex == tabInfo.tab,
                  onTap: () => setState(() => _selectedTabIndex = tabInfo.tab),
                ),
              ),
            )
            .toList(),
      )),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return const WishListDataTable(limit: 5, showInCard: true);
      case 1:
        return VisualGraphList();
      case 2:
        return const MarketDataTable(limit: 5, isHome: true, showInCard: true);
      case 3:
        return MultiBlocProvider(
          providers: [
            BlocProvider<FTradeListCubit>(
              create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: true),
            ),
          ],
          child: FTradeListScreen(showInHomePage: true, showInCard: true),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
