import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/theme/font_pallette.dart';
import '../../domain/models/news/news_model.dart';

class NewsContent extends StatelessWidget {
  final NewsItem news;

  const NewsContent({
    super.key,
    required this.news,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80.gh,
      width: 190.gw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              news.title,
              maxLines: 3,
              style: FontPalette.semiBold14.copyWith(
                color: context.colorTheme.textPrimary,
                height: 1.2,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          8.verticalSpace,
          DefaultTextStyle(
            style: FontPalette.semiBold9.copyWith(
              color: context.colorTheme.textRegular,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(news.comefrom),
                Text(news.publishTime),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
