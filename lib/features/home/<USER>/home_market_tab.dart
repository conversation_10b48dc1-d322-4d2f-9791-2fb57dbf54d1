import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_section.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../shared/theme/font_pallette.dart';
import '../../market/watch_list/widgets/wishlist_data_table.dart';
import '../widgets/market_data_table.dart';

class HomeMarketTabsSection extends StatefulWidget {
  const HomeMarketTabsSection({super.key});

  @override
  State<HomeMarketTabsSection> createState() => _HomeMarketTabsSectionState();
}

class _HomeMarketTabsSectionState extends State<HomeMarketTabsSection> {
  int _selectedTabIndex = 0;
  late List<String> titles;

  @override
  void initState() {
    super.initState();
    if (AppConfig.instance.needShowFutureTrade) {
      titles = ['stockIndex'.tr(), 'stocks'.tr(), 'marketTitle7'.tr(), 'watchList'.tr()];
    } else {
      titles = ['stockIndex'.tr(), 'stocks'.tr(), 'watchList'.tr()];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: _buildPillTabs(),
        ),
        _selectedTabIndex == 2 ? 0.verticalSpace : 16.verticalSpace,
        _buildTabContent(),
      ],
    );
  }

  Widget _buildPillTabs() {
    return Container(
      height: 35.gh,
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: titles.asMap().entries.map((entry) {
          final index = entry.key;
          final title = entry.value;
          return _buildPillTab(index, title);
        }).toList(),
      ),
    );
  }

  Widget _buildPillTab(int index, String title) {
    final isSelected = _selectedTabIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected ? context.appTheme.primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Text(
            title,
            style: FontPalette.medium14.copyWith(
              color: isSelected ? Colors.white : context.appTheme.subTitleColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    final title = titles[_selectedTabIndex];
    if (title == 'stockIndex'.tr()) {
      return VisualGraphSection(isFromHome: true);
    }
    if (title == 'stocks'.tr()) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: const MarketDataTable(limit: 5, isHome: true),
      );
    }
    if (title == 'marketTitle7'.tr()) {
      return MultiBlocProvider(
        providers: [
          BlocProvider<FTradeListCubit>(
            create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: true),
          ),
          // BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
        ],
        child: FTradeListScreen(showInHomePage: true),
      );
    }
    if (title == 'watchList'.tr()) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: const WishListDataTable(limit: 5),
      );
    }
    return const SizedBox.shrink();
  }
}
