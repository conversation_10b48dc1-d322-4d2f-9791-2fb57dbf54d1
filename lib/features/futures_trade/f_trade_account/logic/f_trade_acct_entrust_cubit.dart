import 'dart:async';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_service.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_acct_entrust_state.dart';

class FTradeAcctEntrustCubit extends Cubit<FTradeAcctEntrustState> {
  FTradeAcctEntrustCubit() : super(FTradeAcctEntrustState());

  Timer? _pollTimer;
  String _pollinstrument = '';
  int pageNumber = 1;
  final int _pageSize = 50;

  CancelToken? _cancelToken;

  @override
  Future<void> close() {
    _cancelToken?.cancel();
    _pollTimer?.cancel();
    return super.close();
  }

  // 获取页码*数量 轮询所有数据
  // 总数预计在100条以下, 如果超过100条此处需要重新设计
  // 暂无法计算是否开盘
  void startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
      _fetchListData(instrument: _pollinstrument, loadMore: false, pageNumber: 1, pageSize: pageNumber * _pageSize);
    });
  }

  void resetPolling() {
    startPolling();
  }

  void stopPolling() {
    _pollTimer?.cancel();
  }

  Future<void> fetchData({
    required String instrument,
  }) async {
    _pollinstrument = instrument;

    emit(state.copyWith(
      status: DataStatus.loading,
    ));
    _fetchListData(instrument: instrument, loadMore: false, pageNumber: pageNumber, pageSize: _pageSize);
  }

  Future<void> loadMoreData({
    required String instrument,
  }) async {
    pageNumber += 1;
    _fetchListData(instrument: instrument, loadMore: false, pageNumber: pageNumber, pageSize: _pageSize);
  }

  Future<void> _fetchListData({
    required String instrument,
    required bool loadMore,
    required int pageNumber,
    required int pageSize,
    bool needShowFlutterToast = true,
  }) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();
    FTradeAcctService.fetchFTradeOrderPage(
      instrument: instrument,
      pageNumber: pageNumber,
      pageSize: pageSize,
    ).then((result) {
      if (result != null) {
        if (loadMore) {
          pageNumber = result.current;
          emit(state.copyWith(
            orderModel: result.copyWith(records: (state.orderModel?.records ?? []) + result.records),
            status: DataStatus.success,
          ));
        } else {
          pageNumber = 1;
          emit(state.copyWith(
            orderModel: result,
            status: DataStatus.success,
          ));
        }
      } else {
        emit(state.copyWith(
          status: DataStatus.success,
        ));
        if (needShowFlutterToast) {
          Helper.showFlutterToast(
            'Failed to fetch network error',
          );
        }
      }
    }).catchError((e) {
      emit(state.copyWith(
        status: DataStatus.failed,
      ));
      if (needShowFlutterToast) {
        Helper.showFlutterToast(
          'Failed to fetch network error',
        );
      }
    });
  }
}
