import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/alert_dilaog/custom_alert_dialog.dart';
import '../../../../shared/widgets/buttons/custom_material_button.dart';
import '../../logic/expand_margin/margin_call_cubit.dart';

/// This is a dialog that shows the summary of the contract
/// of type [ContractAction.marginExpand] and [ContractAction.replenish]
class SummaryDialog extends StatelessWidget {
  final ContractAction contractType;
  const SummaryDialog({super.key, required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: CustomAlertDialog(
        hideActionButton: true,
        child: SizedBox(
          child: BlocBuilder<MarginCallCubit, MarginCallState>(
            builder: (context, state) {
              String getCurrency(String? marketType) {
                return switch (marketType) {
                  "CN" => "CNY",
                  "HK" => "HKD",
                  "US" => "USD",
                  _ => "CNY" // Default
                };
              }

              final currency = getCurrency(state.marginCallResponse?.data?.marketType);
              final principal = contractType == ContractAction.replenish
                  ? state.marginCallResponse?.data?.initCash
                  : state.selectedAmount?.toDouble() ?? 0;
              final rate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(currency).rate;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    contractType == ContractAction.replenish
                        ? 'supplementLoss'.tr()
                        : contractType == ContractAction.marginExpand
                            ? 'expandMargin'.tr()
                            : 'renewContract'.tr(),
                    style: context.textTheme.primary.fs16.w600,
                  ),
                  16.verticalSpace,
                  AmountRow(
                    title: 'contractType'.tr(),
                    value: state.marginCallResponse?.data?.contractTypeText?.tr(),
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'period'.tr(),
                    value: state.marginCallResponse?.data?.periodTypeText?.tr(),
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'contractMultiple'.tr(),
                    value: '${state.marginCallResponse?.data?.multiple.toString() ?? ''}${'xTimes'.tr()}',
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: contractType == ContractAction.replenish ? 'marginCall'.tr() : 'contractMargin'.tr(),
                    amount: state.selectedAmount?.toDouble() ?? 0,
                    currency: currency,
                  ),
                  10.verticalSpace,
                  if (contractType != ContractAction.replenish) ...[
                    AmountRow(
                      title: 'totalMargin'.tr(),
                      amount: state.contractModelAmount?.totalTadingFunds ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'lossWarningLine'.tr(),
                      amount: state.contractModelAmount?.lossWarningLine ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'liquidationLine'.tr(),
                      amount: state.contractModelAmount?.lossFlatLine ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'capitalInterestRate'.tr(),
                      amount: state.contractModelAmount?.interestRate ?? 0,
                      currency: '$currency${'period_${state.marginCallResponse?.data?.periodType}_rate'.tr()}',
                    ),
                    10.verticalSpace,
                  ],
                  AmountRow(
                    title: 'actualPaymentAmount'.tr(),
                    amount: contractType == ContractAction.replenish
                        ? (state.selectedAmount?.toDouble() ?? 0) / rate
                        : ((principal ?? 0) + (state.contractModelAmount?.interestRate ?? 0)) / rate,
                    currency: 'CNY',
                    prefix: currency != 'CNY' ? '≈ ' : '',
                  ),
                  16.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: CustomMaterialButton(
                          isOutLined: true,
                          borderColor: context.theme.dividerColor,
                          borderRadius: 10,
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'cancel'.tr(),
                            style: context.textTheme.regular.fs16.w600,
                          ),
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: BlocConsumer<MarginCallCubit, MarginCallState>(
                          listener: (context, state) {
                            if (state.applyMarginCallStatus == DataStatus.success) {
                              GPEasyLoading.showToast('success'.tr());
                              Navigator.pop(context);
                              Navigator.pop(context, true);
                            } else if (state.applyMarginCallStatus == DataStatus.failed) {
                              GPEasyLoading.showToast(state.error ?? 'errorMsg'.tr());
                              Navigator.pop(context);
                            }
                          },
                          builder: (context, state) {
                            return CustomMaterialButton(
                              isLoading: state.applyMarginCallStatus == DataStatus.loading,
                              borderRadius: 10,
                              onPressed: () => (contractType == ContractAction.replenish ||
                                      contractType == ContractAction.marginExpand)
                                  ? context.read<MarginCallCubit>().addExpandMargin(contractType.value)
                                  : context.read<MarginCallCubit>().renewalContract(),
                              child: Text(
                                'submit'.tr(),
                                style: context.textTheme.buttonPrimary.fs16.w600,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
