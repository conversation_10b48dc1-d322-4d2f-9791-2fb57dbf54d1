import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AppInfoBottomSheet extends StatelessWidget {
  const AppInfoBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: BlocBuilder<AppInfoCubit, AppInfoState>(
        builder: (context, state) {
          if (state.status == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state.status == DataStatus.failed) {
            return Center(
              child: Text(
                state.error ?? 'Failed to load content',
                style: context.textTheme.stockRed,
              ),
            );
          } else if (state.status == DataStatus.success) {
            // Find the app info with ID 4
            final appInfo = state.appInfoList.firstWhere(
              (info) => info.type == 4,
              orElse: () => const AppInfoModel(),
            );

            if (appInfo.id == 0) {
              return Center(
                child: Text(
                  'Content not found',
                  style: context.textTheme.regular,
                ),
              );
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Text(
                    appInfo.title,
                    style: context.textTheme.primary.fs18.w700,
                  ),
                ),
                16.verticalSpace,
                Flexible(
                  child: SingleChildScrollView(
                    child: Html(
                      data: appInfo.content,
                    ),
                  ),
                ),
                16.verticalSpace,
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

/// Shows the app info bottom sheet with content ID 4
void showAppInfoBottomSheet(BuildContext context) {
  // Get the AppInfoCubit from the dependency injection
  final appInfoCubit = context.read<AppInfoCubit>();

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.75,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (_, scrollController) => BlocProvider.value(
        value: appInfoCubit,
        child: const AppInfoBottomSheet(),
      ),
    ),
  );
}
