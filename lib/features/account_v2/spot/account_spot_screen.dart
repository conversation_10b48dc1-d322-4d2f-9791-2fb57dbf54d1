import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/account_spot_detail_screen.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';

import '../../../shared/widgets/tab/common_tab_bar.dart';
import '../0_home/domain/view_models/market_category_state.dart';
import '../0_home/account_screen_cubit_v2.dart';
import '../0_home/account_screen_state_v2.dart';

/// 现货账户
class AccountSpotScreen extends StatelessWidget {
  const AccountSpotScreen({super.key});


  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();
    return BlocSelector<AccountScreenCubitV2, AccountScreenStateV2, ({List<MarketCategoryState> list, int index, AccountScreenStateV2 oState})>(
      selector: (state) => (list: state.spotViewModels, index: state.spotScreenCurrentIndex, oState: state),
      builder: (context, state) {

        return Column(
          children: [
            CommonTabBar(
              padding: EdgeInsets.only(left: 5.gw),
              labelPadding: EdgeInsets.symmetric(horizontal: 20.gw),
              backgroundColor: context.appTheme.backgroundColor,
              data: state.list.map((e) => tr(e.category.nameKey)).toList(),
              currentIndex: state.index,
              tabAlignment: TabAlignment.start,
              onTap: (tabIndex) async {
                cubit.updateSpotScreenIndex(tabIndex);

                final viewModel = state.list[tabIndex];
                await cubit.fetchSpotAccountInfo(viewModel: viewModel);
                /// 刷新该市场下所有的订单列表
                for (final entry in viewModel.details.entries) {
                  cubit.fetchMarketOrderList(
                    category: viewModel.category,
                    type: entry.key,
                    orderListState: entry.value,
                  );
                }
              },
            ),
            Expanded(
                child: DirectSlideView(
              pages: state.list.map((e) {
                return AccountSpotDetailScreen(viewModel: e);
                // return AccountSpotDetailScreen(viewModel: e, key: Key(e.category.nameKey));
              }).toList(),
              pageIndex: state.index,
              onPageChanged: (int pageIndex) => cubit.updateSpotScreenIndex(pageIndex),
            ))
          ],
        );
      },
    );
  }
}
