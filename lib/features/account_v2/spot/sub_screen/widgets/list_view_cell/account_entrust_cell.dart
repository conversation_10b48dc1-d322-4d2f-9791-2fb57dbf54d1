import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:shimmer/shimmer.dart';

/// 委托明细Cell
class AccountEntrustCell extends StatelessWidget {
  const AccountEntrustCell({
    super.key,
    required this.data,
    required this.onTap,
    required this.onTapCancelBtn,
    this.isLast = false,
  });

  final FTradeAcctOrderRecords data;
  final VoidCallback onTap;
  final VoidCallback onTapCancelBtn;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final entrustStatus = EntrustStatus.fromValueByValue(data.status);
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: isLast
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(10.gr),
                    bottomRight: Radius.circular(10.gr),
                  )
                : null),
        padding: EdgeInsets.fromLTRB(6.gw, 8.gw, 0, 8.gw),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Name and Symbol Column
            Expanded(
              flex: 6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.symbolName,
                    style: context.textTheme.regular.w600,
                  ),
                  Row(
                    spacing: 5.gh,
                    children: [
                      SymbolChip(
                        name: data.market,
                        chipColor: context.theme.primaryColor,
                      ),
                      Text(
                        data.symbol,
                        style: context.textTheme.regular.fs12,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            Expanded(
                flex: 5,
                child: AnimatedFlipCounter(
                  fractionDigits: 2,
                  decimalSeparator: '.',
                  thousandSeparator: ',',
                  textStyle: context.textTheme.primary.w700.ffAkz,
                  value: data.tradePrice,
                )),

            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Text(
                    data.dealNum.toNumeric,
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.w600.ffAkz,
                  ),
                  Text(
                    data.tradeNum.toNumeric,
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.w600.ffAkz,
                  ),
                ],
              ),
            ),

            Expanded(
              flex: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${TradeDirection.fromValue(data.direction).translationKey.tr()}${TradeType.fromValue(data.tradeType).translationKey.tr()}',
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.fs12.copyWith(
                        color:
                            TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(data.direction))),
                  ),
                  Text(
                    entrustStatus.label.tr(),
                    textAlign: TextAlign.right,
                    style: context.textTheme.regular.fs10.w600.copyWith(color: entrustStatus.color),
                  ),
                ],
              ),
            ),
            8.horizontalSpace,
            Expanded(
              flex: 3,
              child: InkWell(
                onTap: onTapCancelBtn,
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    'revoke'.tr(),
                    textAlign: TextAlign.right,
                    style: context.textTheme.stockRed.fs12.w700,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class AccountEntrustShimmerCell extends StatelessWidget {
  const AccountEntrustShimmerCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.gw),
      padding: EdgeInsets.fromLTRB(6.gw, 8.gw, 10.gw, 8.gw),
      color: Colors.white,
      child: Shimmer.fromColors(
        baseColor: context.theme.dividerColor,
        highlightColor: context.theme.cardColor.withValues(alpha: 0.5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧：名称 + 代码
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(36, 10),
              ],
            ),

            // 中间：成交价格
            Align(
              alignment: Alignment.centerRight,
              child: _box(45, 10),
            ),

            // 中间：委托数量 + 成交数量
            Column(
              children: [
                _box(40, 10),
                const SizedBox(height: 1),
                _box(40, 10),
              ],
            ),

            // 中间：买入卖出 + 状态
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(40, 10),
              ],
            ),

            // 右侧：撤单按钮
            Align(
              alignment: Alignment.centerRight,
              child: _box(28, 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
