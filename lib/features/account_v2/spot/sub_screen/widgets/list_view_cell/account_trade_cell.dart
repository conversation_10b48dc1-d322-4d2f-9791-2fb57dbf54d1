import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:shimmer/shimmer.dart';

/// 成交明细Cell
class AccountTradeCell extends StatelessWidget {
  const AccountTradeCell({
    super.key,
    required this.data,
    required this.onTap,
    this.isLast = false,
  });

  final FTradeAcctOrderRecords data;
  final VoidCallback onTap;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: isLast
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(10.gr),
                    bottomRight: Radius.circular(10.gr),
                  )
                : null),
        padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 8.gw),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Name and Symbol Column
            Expanded(
              flex: 6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 5.gh,
                children: [
                  Text(
                    data.symbolName,
                    style: FontPalette.semiBold14,
                  ),
                  Row(
                    spacing: 5.gh,
                    children: [
                      SymbolChip(name: data.market, chipColor: context.appTheme.primaryColor),
                      Text(
                        data.symbol,
                        style: FontPalette.semiBold12.copyWith(
                          color: context.appTheme.subTitleColor,
                          fontFamily: 'Akzidenz-Grotesk',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Latest Price Column
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Text(
                    data.tradePrice.toStringAsFixed(2),
                    textAlign: TextAlign.center,
                    style:
                        FontPalette.normal14.copyWith(fontFamily: 'Akzidenz-Grotesk', color: ColorPalette.primaryColor),
                  ),
                  Text(
                    data.tradeNum.toStringAsFixed(1),
                    textAlign: TextAlign.center,
                    style: FontPalette.normal12.copyWith(color: ColorPalette.customColor),
                  ),
                ],
              ),
            ),
            Expanded(
                flex: 5,
                child: AnimatedFlipCounter(
                  fractionDigits: 2,
                  decimalSeparator: '.',
                  thousandSeparator: ',',
                  textStyle: FontPalette.bold14.copyWith(
                    color: ColorPalette.primaryColor,
                    fontFamily: 'Akzidenz-Grotesk',
                  ),
                  value: data.transactionAmount ?? 0.00,
                )),
            // Change Percentage Column
            Expanded(
              flex: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${TradeDirection.fromValue(data.direction ?? 0).translationKey.tr()}${TradeType.fromValue(data.tradeType ?? 0).translationKey.tr()}',
                    textAlign: TextAlign.center,
                    style: FontPalette.normal12.copyWith(
                        color: TradeDirection.getColor(context,
                            tradeDirection: TradeDirection.fromValue(data.direction ?? 0))),
                  ),
                  Text(
                    ConvertHelper.formatDateTypeIn24Hour(data.dealTime ?? ''),
                    textAlign: TextAlign.center,
                    style: FontPalette.semiBold10.copyWith(
                      color: ColorPalette.subTitleColor,
                      // fontFamily: 'Akzidenz-Grotesk',
                      fontSize: 8.gh,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AccountTradeShimmerCell extends StatelessWidget {
  const AccountTradeShimmerCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.gw),
      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 8.gw),
      color: Colors.white,
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧：名称 + 市场 + 代码
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(36, 10),
              ],
            ),

            // 成交价 + 成交量
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _box(50, 10),
                const SizedBox(height: 1),
                _box(40, 10),
              ],
            ),

            // 成交金额
            Align(
              alignment: Alignment.center,
              child: _box(60, 10),
            ),

            // 买卖方向 + 时间
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(50, 10),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
