import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ChatTheme {
  static TUITheme lightChatTheme(BuildContext context) {
    return TUITheme(
      // Primary Colors
      primaryColor: context.theme.primaryColor,
      secondaryColor: context.theme.primaryColor.withValues(alpha: 0.7),
      weakBackgroundColor: context.theme.scaffoldBackgroundColor,
      wideBackgroundColor: Colors.grey[100],
      weakDividerColor: ColorPalette.cardColor,
      chatTimeDividerTextColor: ColorPalette.dividerColor,

      // Message Bubble Colors
      chatMessageItemFromSelfBgColor: context.theme.primaryColor,
      chatMessageItemFromOthersBgColor: ColorPalette.cardColor,

      chatMessageItemTextColor: Colors.black,

      // Header Colors
      chatHeaderBgColor: ColorPalette.whiteColor,
      chatHeaderTitleTextColor: Colors.black,
      chatHeaderBackTextColor: Colors.black,
      chatHeaderActionTextColor: Colors.black,

      // Background Colors
      chatBgColor: ColorPalette.backgroundColor,

      // Text Colors
      textColor: Colors.black,
      darkTextColor: Colors.white,
      weakTextColor: Colors.grey,

      // Other Specific Colors
      white: Colors.white,
      black: Colors.black,
      cautionColor: Colors.red,
      appbarBgColor: ColorPalette.backgroundColor,
    );
  }

  static TUITheme darkChatTheme(BuildContext context) {
    return TUITheme(
      // Primary Colors
      primaryColor: context.theme.primaryColorDark,
      secondaryColor: context.theme.primaryColorDark.withValues(alpha: 0.7),
      weakBackgroundColor: context.theme.scaffoldBackgroundColor,
      wideBackgroundColor: ColorPalette.cardColorDark,
      weakDividerColor: ColorPalette.cardColorDark,
      chatTimeDividerTextColor: ColorPalette.cardColorDark,

      // Message Bubble Colors
      chatMessageItemFromSelfBgColor: ColorPalette.primaryColorDark,
      chatMessageItemFromOthersBgColor: Colors.grey[800],
      chatMessageItemTextColor: Colors.white,

      // Header Colors
      chatHeaderBgColor: ColorPalette.backgroundColorDark,
      chatHeaderTitleTextColor: Colors.white,
      chatHeaderBackTextColor: Colors.white,
      chatHeaderActionTextColor: Colors.white,

      // Background Colors
      chatBgColor: ColorPalette.backgroundColorDark,

      // Text Colors
      textColor: Colors.white,
      darkTextColor: Colors.white70,
      weakTextColor: Colors.grey,

      // Other Specific Colors
      white: Colors.white,
      black: Colors.black,
      cautionColor: Colors.redAccent,

      appbarBgColor: ColorPalette.backgroundColorDark,
    );
  }

  // Method to set the chat theme based on the current app theme
  static void setChatTheme(BuildContext context) {
    final isLightMode = Theme.of(context).brightness == Brightness.light;
    final timCoreInstance = TIMUIKitCore.getInstance();

    timCoreInstance.setTheme(
      theme: isLightMode ? lightChatTheme(context) : darkChatTheme(context),
    );
  }
}

class TUIChatTheme extends StatelessWidget with TUIChatThemeCore {
  const TUIChatTheme({
    super.key,
    required this.child,
    required this.customTheme,
  });

  final Widget child;
  final TUITheme customTheme;

  @override
  Widget build(BuildContext context) {
    Helper.afterInit(() => setTheme(customTheme));
    return Theme(
      data: ThemeData(
        extensions: <ThemeExtension<dynamic>>[
          if (customTheme.primaryColor == context.theme.primaryColor ) ...[],
          if (customTheme.primaryColor == ColorPalette.primaryColorDark) ...[],
        ],
        brightness: customTheme.primaryColor == ColorPalette.primaryColorDark ? Brightness.dark : Brightness.light,
        scaffoldBackgroundColor: context.theme.scaffoldBackgroundColor,
      ),
      child: child,
    );
  }
}

// extension TUIChatThemeExtension on TUITheme {
//   Color get myChatColor => const Color(0xFF4366DE);
//   Color get myOtherChatColor => const Color(0xFFFF0000);
// }

// extension BuildContextExtension on BuildContext {
//   MyColorScheme get colorScheme => Theme.of(this).extension<MyColorScheme>() ?? MyColorScheme.lightScheme;
// }

extension ThemeExtensions on BuildContext {
  bool get isLightMode => Theme.of(this).brightness == Brightness.light;
}

mixin TUIChatThemeCore {
  void setTheme(TUITheme customTheme) {
    final timCoreInstance = TIMUIKitCore.getInstance();
    timCoreInstance.setTheme(theme: customTheme);
  }
}
