import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/chat/domain/models/models/cm_product_model.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/cm_mentor.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/support_dp.dart';
import 'package:gp_stock_app/shared/widgets/error/shared_error.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/share_card.dart';

class ChatScreen extends StatefulWidget {
  final V2TimConversation? selectedConversation;
  final String? nickName;
  final V2TimFriendInfo? freindInfo;
  final int? messagingFrequency;

  const ChatScreen({super.key, this.selectedConversation, this.nickName, this.freindInfo, this.messagingFrequency});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  @override
  Widget build(BuildContext context) {
    if (widget.selectedConversation == null) {
      return const SharedError();
    }
    return TIMUIKitChat(
      chatScreenDecoration: BoxDecoration(
        image: DecorationImage(
          image: context.isLightMode
              ? const AssetImage("assets/images/background.png")
              : const AssetImage("assets/images/background_dark.png"),
          fit: BoxFit.cover,
        ),
      ),
      locale: NetworkHelper.currentLocale,
      groupID: widget.selectedConversation?.groupID,
      messagingFrequency: widget.messagingFrequency,
      messageItemBuilder: MessageItemBuilder(
        customMessageItemBuilder: (message, isShowJump, clearJump) {
          try {
            final parsedJson = json.decode(message.customElem!.data!);
            if (parsedJson['type'] == "cm_product") {
              final data = CMProductModel.fromJson(parsedJson['data']);
              return CMProduct(data: data.toJson());
            }

            if (parsedJson['type'] == "cm_mentor") {
              final id = int.tryParse('${parsedJson['data']['id']}');
              return CMMentor(id: id);
            }
          } catch (e) {
            return SizedBox.shrink();
          }
          return SizedBox.shrink();
        },
      ),
      conversation: widget.selectedConversation!,
      // TODO
      // customStickerPanel: renderCustomStickerPanel,
      config: TIMUIKitChatConfig(
        isAllowClickAvatar: false,
        isUseDefaultEmoji: true,
        isAllowLongPressMessage: true,
        isShowReadingStatus: true,
        isShowGroupReadingStatus: true,
        notificationTitle: "",
        isUseMessageReaction: true,
        isShowAvatar: widget.selectedConversation?.groupID != null,
        // groupReadReceiptPermissionList: [
        //   GroupReceiptAllowType.work,
        //   GroupReceiptAllowType.meeting,
        //   GroupReceiptAllowType.public,
        // ],
      ),
      userAvatarBuilder: (context, message, canShow) {
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: Dp(
            faceUrl: message.faceUrl,
            type: 1,
            userId: message.nickName,
            name: message.nickName,
            size: 42,
          ),
        );
      },
      appBarConfig: AppBar(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        foregroundColor: context.colorTheme.textPrimary,
        surfaceTintColor: context.theme.primaryColor,
        titleTextStyle: TextStyle(
          color: context.colorTheme.textPrimary,
        ),
        iconTheme: IconThemeData(
          color: context.colorTheme.textPrimary,
        ),
        actionsIconTheme: IconThemeData(
          color: context.colorTheme.textPrimary,
        ),
        centerTitle: false,
        leadingWidth: 50,
        leading: const BackButton(),
        elevation: 0,
        title: GestureDetector(
          onTap: () async {
            final conversationType = widget.selectedConversation?.type ?? 1;

            if (conversationType == 1) {
              return;
            } else if (conversationType == 2) {
              // final String? groupID = widget.selectedConversation?.groupID;
              // TODO
              // if (groupID != null) {
              //   Navigator.pushNamed(
              //     context,
              //     routeGroupProfileScreen,
              //     arguments: groupID,
              //   );
              // }
            }
          },
          child: Row(
            children: [
              if (widget.freindInfo != null &&
                  (widget.freindInfo?.userProfile?.role == 1 || widget.freindInfo?.userProfile?.role == 2))
                SupportDp(
                  faceUrl: widget.selectedConversation?.faceUrl,
                  userId: widget.selectedConversation?.userID ?? widget.selectedConversation?.groupID,
                  name: widget.freindInfo?.userProfile?.nickName ?? widget.selectedConversation?.showName,
                  size: 38,
                )
              else
                Dp(
                  faceUrl: widget.selectedConversation?.faceUrl,
                  type: widget.selectedConversation?.type,
                  size: 38,
                  userId: widget.selectedConversation?.userID ?? widget.selectedConversation?.groupID,
                  name: widget.freindInfo?.userProfile?.nickName ?? widget.selectedConversation?.showName,
                ),
              const SizedBox(width: 16),
              Text(
                widget.freindInfo?.userProfile?.nickName ?? widget.selectedConversation?.showName ?? '',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: context.colorTheme.textPrimary,
                ),
              ),
            ],
          ),
        ),
        scrolledUnderElevation: 0,
      ),
    );
  }
}
