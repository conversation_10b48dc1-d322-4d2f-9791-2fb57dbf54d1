import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/real_name_tips_dialog.dart';
import 'package:gp_stock_app/features/profile/logic/auth_n/auth_n_cubit.dart';
import 'package:gp_stock_app/features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';

import '../../features/market/utils/utils.dart';
import '../../features/profile/domain/models/auth_n/info/auth_n_info.dart';
import '../../shared/constants/enums.dart';
import '../../shared/logic/sort_color/sort_color_cubit.dart';
import '../../shared/models/sys_settings_model/sys_settings_model.dart';
import '../../shared/theme/color_pallette.dart';
import '../../shared/widgets/alert_dilaog/auth_dialog.dart';

extension SizedBoxExtension on num {
  SizedBox get verticalSpace => SizedBox(height: toDouble().gh);
  SizedBox get horizontalSpace => SizedBox(width: toDouble().gw);
}

extension ImageExtension on String {
  SvgPicture svg(double width, double height) => SvgPicture.asset(this, width: width, height: height);
}

extension Base64Extension on String {
  String toBase64() => base64.encode(utf8.encode(this));
  String fromBase64() => utf8.decode(base64.decode(this));
}

extension MarketParamsX on MarketParams {
  /// Creates a new instance with updated page size
  MarketParams copyWithPageSize(int pageSize) => MarketParams(
        marketType: marketType,
        todaysTab: todaysTab,
        sortType: sortType,
        orderType: orderType,
        isHome: isHome,
        pageNum: pageNum,
        pageSize: pageSize,
      );
}

extension SignedInExtension on BuildContext {
  Future<void> verifyAuth(VoidCallback onTap) async {
    final isSignedIn = read<SignInCubit>().isLoggedIn;
    if (isSignedIn) {
      onTap();
    } else {
      final result = await showDialog<bool>(
        context: this,
        builder: (dialogContext) => AuthRequiredDialog(),
      );

      if (result == true) {
        Future.delayed(const Duration(milliseconds: 300), () => onTap());
      }
    }
  }

  Future<void> verifyRealName(VoidCallback onTap) async {
    final isVerified = read<AuthNCubit>().isVerified;
    if (isVerified) {
      onTap();
    } else {
      GPEasyLoading.showLoading(message: "verifying".tr());
      final isVerified = await read<AuthNCubit>().getAuthNInfo();
      GPEasyLoading.dismiss();
      if (isVerified) {
        onTap();
      } else {
        final flag = await RealNameTipsDialog.show(context: this);
        if (flag) {
          onTap.call();
        }
      }
    }
  }
}

extension NumberFormatterExtension on num {
  /// Formats a number with comma separators for thousands
  /// Example: 1000 -> 1,000, 1000000 -> 1,000,000
  String formatWithCommas() {
    // Convert to double to ensure decimal representation
    final doubleValue = toDouble();

    // Use NumberFormat to handle comma separators and always show 2 decimal places
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return formatter.format(doubleValue);
  }
}

extension CertificateStatusExtension on AuthNInfo {
  String toCertificateStatusText() {
    if (status == 1) {
      return 'approved'.tr();
    }

    if (certificateType != 0 && id != 0) {
      return 'pending'.tr();
    }

    return '';
  }

  Color toCertificateStatusColor() {
    if (status == 1) {
      return ColorPalette.greenColor;
    }

    if (certificateType != 0 && id != 0) {
      return ColorPalette.yellowColor;
    }

    return ColorPalette.greyColor;
  }
}

int getDigitPrecision(num? digit) {
  // Handle null case
  if (digit == null) return 2;

  // Integer case: always return 2 decimal places
  if (digit is int || digit == digit.toInt()) return 2;

  // Convert to string and split by decimal point
  final parts = digit.toString().split('.');

  // If we have a decimal part, return its length, otherwise return 2
  return parts.length > 1 ? parts[1].length : 2;
}

// Extension method for easier use with Flutter widgets
extension PrecisionFormatter on num? {
  String toFormattedString() {
    if (this == null) return '0.00';

    // Get the precision based on the number
    final precision = getDigitPrecision(this);

    // Format with the determined precision
    return this!.toStringAsFixed(precision);
  }
}

/// Extension to convert double to rounded string representation
extension DoubleRounding on double {
  /// Converts a double to a string with specified decimal places
  /// If [decimalPlaces] is not provided, it defaults to 2
  String toRoundedString({int decimalPlaces = 2}) {
    return toStringAsFixed(decimalPlaces);
  }
}

/// Extension to get color based on numeric value and market color setting
extension NumColorExtension on num {
  Color getValueColor(BuildContext context) {
    if (this == 0) return context.colorTheme.tabInactive;
    final marketColor = context.watch<SortColorCubit>().state.marketColor;
    return marketColor == MarketColor.redUpGreenDown
        ? this <= 0
            ? context.colorTheme.stockGreen
            : context.colorTheme.stockRed
        : this <= 0
            ? context.colorTheme.stockRed
            : context.colorTheme.stockGreen;
  }
}

extension NumericStringExtension on num? {
  String get toNumeric {
    if (this == null) return '0';
    return this! % 1 == 0 ? this!.toInt().toString() : this!.toString();
  }
}

extension SortColorExtension on MarketColor {
  Color upColor(BuildContext context) => switch (this) {
        MarketColor.redUpGreenDown => context.colorTheme.stockRed,
        MarketColor.greenUpRedDown => context.colorTheme.stockGreen,
      };

  Color downColor(BuildContext context) => switch (this) {
        MarketColor.redUpGreenDown => context.colorTheme.stockGreen,
        MarketColor.greenUpRedDown => context.colorTheme.stockRed,
      };

  String get tr => switch (this) {
        MarketColor.redUpGreenDown => 'redUpGreenDown',
        MarketColor.greenUpRedDown => 'greenUpRedDown',
      };
}

extension SortColorExtension2 on BuildContext {
  Color get upColor => watch<SortColorCubit>().state.marketColor.upColor(this);
  Color get downColor => watch<SortColorCubit>().state.marketColor.downColor(this);
}

extension MultiLanguageTextExtension on MultiLanguageText? {
  /// Returns the text corresponding to the provided locale
  /// If the specific locale text is not available, falls back to enUS
  String getLocalizedText(Locale locale) {
    if (this == null) return '';

    // First check for exact match
    if (locale.languageCode == 'en' && locale.countryCode == 'US') {
      return this!.enUS ?? '';
    } else if (locale.languageCode == 'zh' && locale.countryCode == 'CN') {
      return this!.zhCN ?? this!.enUS ?? '';
    } else if (locale.languageCode == 'zh' && locale.countryCode == 'TW') {
      // Since zhTW isn't available in your class, fall back to HK (Traditional Chinese) or CN
      return this!.zhHK ?? this!.zhCN ?? this!.enUS ?? '';
    } else if (locale.languageCode == 'zh' && locale.countryCode == 'HK') {
      return this!.zhHK ?? this!.enUS ?? '';
    }

    // If no exact match, check for language-only match
    if (locale.languageCode == 'en') {
      return this!.enUS ?? '';
    } else if (locale.languageCode == 'zh') {
      // For generic Chinese, prefer in this order: CN, HK, or fallback to English
      return this!.zhCN ?? this!.zhHK ?? this!.enUS ?? '';
    }

    // Fallback to English
    return this!.enUS ?? '';
  }

  /// Returns the text for the current locale, or falls back to enUS if not available
  String get currentLocaleText {
    final currentLocale = navigatorKey.currentContext!.locale;
    return getLocalizedText(currentLocale);
  }
}

// extension BuildContextExtension on BuildContext {
//   MyColorScheme get colorScheme => Theme.of(this).extension<MyColorScheme>() ?? MyColorScheme.lightScheme;
// }

extension ThemeExtensions on BuildContext {
  bool get isLightMode => Theme.of(this).brightness == Brightness.light;
}
