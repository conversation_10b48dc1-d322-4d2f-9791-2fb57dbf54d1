//singleton for secure storage
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';

class SecureStorageHelper {
  static final SecureStorageHelper _secureStorageHelper = SecureStorageHelper._internal();

  factory SecureStorageHelper() {
    return _secureStorageHelper;
  }

  SecureStorageHelper._internal();

  final _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(),
  );

  Future<void> writeSecureData(String key, String value) async {
    await _storage.write(
      key: key,
      value: value,
    );
  }

  Future<String?> readSecureData(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> deleteSecureData(String key) async {
    await _storage.delete(key: key);
  }

  /// 需要通知更新数据删除
  ///
  /// Must noti AuthUtils delected token
  ///
  /// AuthUtils.instance.notifyUpdate(token: null);
  Future<void> deleteAllSecureData() async {
    await _storage.deleteAll();
  }

  // Keys to keep
  final List<String> _keysToKeep = [
    LocalStorageKeys.isRememberPassword,
    LocalStorageKeys.username,
    LocalStorageKeys.password,
  ];

  /// 需要通知更新数据删除
  ///
  /// Must noti AuthUtils delected token
  ///
  /// AuthUtils.instance.notifyUpdate(token: null);
  Future<void> deleteAllExcept() async {
    final Map<String, String> allValues = await _storage.readAll();
    for (var key in allValues.keys) {
      if (!_keysToKeep.contains(key)) {
        await _storage.delete(key: key);
      }
    }
  }
}
