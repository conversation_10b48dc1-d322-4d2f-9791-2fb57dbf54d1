abstract class BaseScreenDataRepository<T> {
  final Duration _cacheDuration = const Duration(minutes: 5);
  T? _cachedData;
  DateTime? _lastUpdated;

  void updateCache(T newData) {
    _cachedData = newData;
    _lastUpdated = DateTime.now();
  }

  bool get isExpired {
    if (_lastUpdated == null) return true;
    return DateTime.now().difference(_lastUpdated!) > _cacheDuration;
  }

  void checkAndClearCacheIfExpired() {
    if (_cachedData == null && _lastUpdated == null) {
      return;
    }
    if (isExpired) {
      clearCache();
    }
  }

  void clearCache() {
    _cachedData = null;
    _lastUpdated = null;
  }

  T? get cachedData => _cachedData;
}
