import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/api/network/models/response_model.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/custom_alert_dialog.dart';

import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/theme/color_pallette.dart';
import '../../../shared/theme/font_pallette.dart';
import '../../../shared/theme/my_color_scheme.dart';

class NetworkHelper {
  static String? get currentLocale {
    if (navigatorKey.currentContext == null) return null;
    return '${navigatorKey.currentContext!.locale.languageCode}-${navigatorKey.currentContext!.locale.countryCode}';
  }

  static Future<void> handleMessage(
    String? message,
    BuildContext context, {
    bool useParentContext = false,
    String? actionButtonText,
    bool? hideHeader,
    String? headerImage,
    VoidCallback? onTap,
    String title = 'Error',
    String? buttonText,
    HandleTypes type = HandleTypes.snackbar,
    IconData? icon,
    Color? color,
    Color backgroundColor = Colors.black,
    bool isWarning = false,
    bool isInfinite = false,
    double bottomPadding = 50,
    SnackBarType snackBarType = SnackBarType.validation,
    String? dialogKey,
  }) async {
    // if (!context.mounted) return;
    // return;
    switch (type) {
      case HandleTypes.customDialog:
        Helper.showDialogPopUp(
          context,
          CustomAlertDialog(
            title: kDebugMode ? title : null,
            message: message ?? 'errorMsg'.tr(),
            actionButtonText: actionButtonText ?? 'ok'.tr(),
            buttonBackGroundColor: context.appTheme.primaryColor,
            onActionButtonPressed: onTap ??
                () {
                  Navigator.pop(context);
                },
            headerImage: (hideHeader ?? false) ? null : headerImage ?? _getHeaderImage(snackBarType),
            isLoading: false,
            messageTextStyle: FontPalette.semiBold20.copyWith(color: context.appTheme.titleColor),
          ),
          dialogKey: dialogKey,
          barrierDismissible: snackBarType == SnackBarType.error ? true : false,
        );
        break;
      case HandleTypes.snackbar:
        showSnackBar(
          message ?? 'errorMsg'.tr(),
          context,
          backgroundColor: backgroundColor,
          bottomPadding: bottomPadding,
          snackBarType: snackBarType,
          isWarning: isWarning,
          isInfinite: isInfinite,
          color: color,
          icon: icon,
        );
        break;
      default:
    }
  }

  static ResponseModel<T> mappingResponseData<T>(Response response) {
    if ([200, 201].contains(response.statusCode)) {
      return ResponseModel<T>.fromJson(response.data);
    } else {
      if (kDebugMode) {
        Helper.showFlutterToast(
          'Server response exception ${response.statusCode}',
        );
      }
      return ResponseModel(
        data: null,
        code: response.statusCode,
        msg: 'Server response exception ${response.statusCode}',
      );
    }
  }
}

String _getHeaderImage(SnackBarType snackBarType) {
  switch (snackBarType) {
    case SnackBarType.error:
      return Assets.alertError;
    case SnackBarType.success:
      return Assets.alertSuccess;
    case SnackBarType.validation:
      return Assets.alertValidation;
    default:
      return Assets.alertError;
  }
}

void showSnackBar(
  String message,
  BuildContext context, {
  IconData? icon,
  Color? color,
  Color backgroundColor = Colors.black,
  bool isWarning = false,
  bool isInfinite = false,
  double bottomPadding = 50,
  SnackBarType snackBarType = SnackBarType.validation,
}) {
  ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(
      SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        content: Container(
          constraints: BoxConstraints(minHeight: 40.gh, maxHeight: 50.gh),
          margin: EdgeInsets.only(bottom: bottomPadding - 40),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: getSnackBarColor(snackBarType, context),
            borderRadius: BorderRadius.circular(8.gr),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      message,
                      maxLines: 10,
                      style: TextStyle(
                        fontSize: 15.gh,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                if (snackBarType == SnackBarType.validation)
                  isWarning
                      ? const SizedBox.shrink()
                      : Icon(
                          icon ?? Icons.error,
                          size: 20,
                          color: Colors.red,
                        ),
                // else
                //   getAnimatedIcon(snackBarType)
              ],
            ),
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
        ),
        duration: isInfinite ? const Duration(days: 1) : Duration(seconds: message.length > 40 ? 5 : 2),
      ),
    );
}

Color getSnackBarColor(SnackBarType snackBarType, BuildContext context) {
  if (snackBarType == SnackBarType.success) {
    return ColorPalette.greenColor;
  } else if (snackBarType == SnackBarType.error) {
    return ColorPalette.redColor;
  } else if (snackBarType == SnackBarType.info) {
    return ColorPalette.blueColor;
  } else if (snackBarType == SnackBarType.warning) {
    return ColorPalette.yellowColor;
  } else if (snackBarType == SnackBarType.validation) {
    return ColorPalette.redAccentColor;
  }
  return Colors.black;
}
