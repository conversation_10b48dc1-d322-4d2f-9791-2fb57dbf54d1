import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';

/** {
  "msg": "success",
  "code": 1,
  "data": [
    {
      "key": "value",
    }
  ],
} */

/** {
  "msg": "success",
  "code": 1,
  "data": {
    "key": "value",
  },
} */

///
class ResponseModel<T> {
  T? data;
  int? code;
  String? msg;

  ResponseModel({
    required this.data,
    required this.code,
    required this.msg,
  });

  ResponseModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null && json['data'] != 'null') {
      var processedData = json['data'];

      if (processedData is List) {
        Map<String, dynamic> wrappedJson = {"list": processedData};
        data = JsonConvert.fromJsonAsT<T>(wrappedJson);
      } else {
        data = JsonConvert.fromJsonAsT<T>(processedData);
      }
    }
    code = json['code'];
    msg = json['msg'];
  }
}
