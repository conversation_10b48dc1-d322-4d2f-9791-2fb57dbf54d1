import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/models/entities/account_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract_summary_page_entity.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';

class AccountApi {
  /// 获取账号金额相关信息
  static Future<AccountInfo?> fetchAccountInfo({MarketCategory? category}) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getAccountInfo,
      isAuthRequired: true,
      queryParameters: {
        if (category != null) "dataType": category.code,
      }
    );
    final responseModel = NetworkHelper.mappingResponseData<AccountInfo>(response);
    return responseModel.data;
  }

  /// 获取合约列表 isSettlement：是否已结算
  static Future<ContractSummaryPageEntity?> fetchContractSummary({int page = 1, bool isSettlement = false}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getContractSummaryPage,
        isAuthRequired: true,
        force: true,
        queryParameters: {
          'settlementStatus': isSettlement ? 2 : 1,
          'pageNumber': page,
          'pageSize': 20,
        },
      );
      final responseModel = NetworkHelper.mappingResponseData<ContractSummaryPageEntity>(response);
      return responseModel.data;
    } catch (e) {
      return null;
    }

  }

  /// 获取现货订单详情
  static Future<PositionEntity?> fetchPositionDetail({required int id}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getPositionDetail}/$id',
        isAuthRequired: true,
      );
      final responseModel = NetworkHelper.mappingResponseData<PositionEntity>(response);
      return responseModel.data;
    } catch (e) {
      return null;
    }
  }

  /// 获取合约详情
  static Future<ContractSummaryPageRecord?> fetchContractDetail({required int contractId}) async {
    final Response response = await NetworkProvider().get(
      '${ApiEndpoints.getContractSummary}/$contractId',
      isAuthRequired: true,
    );
    final responseModel = NetworkHelper.mappingResponseData<ContractSummaryPageRecord>(response);
    return responseModel.data;
  }

  /// 持仓列表
  static Future<FTradeAcctOrderModel?> getPositionList({
    int page = 1,
    int? pageSize = 20,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {
    try {
      final Response response =
          await NetworkProvider().get(ApiEndpoints.getPositionList, isAuthRequired: true, queryParameters: {
        'pageNumber': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      });

      final responseModel = NetworkHelper.mappingResponseData<FTradeAcctOrderModel>(response);
      return responseModel.data;
    } catch (e) {
      return null;
    }
  }

  /// 订单列表：委托明细、成交明细
  static Future<FTradeAcctOrderModel?> getOrderList({
    int page = 1,
    int? pageSize = 20,
    int? status, // 订单委托状态： 0 委托中 1委托撤销 2.订单成交成功 3.合约到期自动撤销
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {

    /// https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/pageUsingGET_11
    try {
      final Response response =
      await NetworkProvider().get(ApiEndpoints.getOrderList, isAuthRequired: true, queryParameters: {
        'pageNum': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (status != null) 'status': status,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      });

      final responseModel = NetworkHelper.mappingResponseData<FTradeAcctOrderModel>(response);
      return responseModel.data;
    } catch (e) {
      return null;
    }
  }
}
