import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';

/// 期货相关接口
class FutureApi {
  /// 设置期货止盈止损
  static Future<bool> setFStopLine({
    required int positionId,
    double? takeProfitValue,
    double? stopLossValue,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.setFutureStopLine,
        data: {
          'positionId': positionId,
          if (takeProfitValue != null) 'takeProfitValue': takeProfitValue,
          if (stopLossValue != null) 'stopLossValue': stopLossValue,
        },
        isAuthRequired: true,
      );
      return response.data["data"] == true;
    } catch (E) {
      return false;
    }
  }

  /// 期货 追加保证金
  static Future<bool> addMargin({
    required int positionId,
    required double amount,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.addFutureMargin,
        data: {
          'positionId': positionId,
          'addMarginAmount': amount,
        },
        isAuthRequired: true,
      );
      return response.data["data"] == true;
    } catch (E) {
      return false;
    }
  }
}
