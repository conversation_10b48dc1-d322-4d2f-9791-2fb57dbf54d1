import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/contract_summary_page_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/contract_summary_page_entity.g.dart';

@JsonSerializable()
class ContractSummaryPageEntity {
	int current = 0;
	bool hasNext = false;
	List<ContractSummaryPageRecord> records = [];
	int total = 0;

	ContractSummaryPageEntity();

	factory ContractSummaryPageEntity.fromJson(Map<String, dynamic> json) => $ContractSummaryPageEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractSummaryPageEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractSummaryPageRecord {
	double accountWinAmount = 0.0;
	double allAsset = 0.0;
	double closeRemindAmount = 0.0;
	double contractAssetAmount = 0.0;
	double coverLossAmount = 0.0;
	String currency = '';
	double expendAmount = 0.0;
	String expireTime = '';
	double freezePower = 0.0;
	double gapCloseRemindAmount = 0.0;
	double gapWarnRemindAmount = 0.0;
	double giveAmount = 0.0;
	int id = 0;
	double initCash = 0.0;
	double interestAmount = 0.0;
	double interestRate = 0.0;
	String marketType = '';
	int multiple = 0;
	double negativeAmount = 0.0;
	String openTime = '';
	int periodType = 0;
	double positionAmount = 0.0;
	double receivableInterest = 0.0;
	int settlementStatus = 0;
	double todayWinAmount = 0.0;
	double todayWinRate = 0.0;
	int totalAccountAmount = 0;
	double totalCash = 0.0;
	double totalFinance = 0.0;
	double totalPower = 0.0;
	int type = 0;
	double useAmount = 0.0;
	double warnRemindAmount = 0.0;
	double winRate = 0.0;
	double winAmount = 0.0;
	double withdrawAmount = 0.0;
	int yesterdayAsset = 0;
	bool isAutoRenew = false;

	ContractSummaryPageRecord();

	factory ContractSummaryPageRecord.fromJson(Map<String, dynamic> json) => $ContractSummaryPageRecordFromJson(json);

	Map<String, dynamic> toJson() => $ContractSummaryPageRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}