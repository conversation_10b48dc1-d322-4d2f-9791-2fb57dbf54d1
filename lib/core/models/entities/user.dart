import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/user.g.dart';
import 'dart:convert';

@JsonSerializable()
class UserModel {
	bool auth = false;
	int authStatus = 0;
	String avatar = '';
	String countryCode = '';
	String email = '';
	int fromType = 0;
	int id = 0;
	String idCard = '';
	String imAccount = '';
	String inviteCode = '';
	bool isPayment = false;
	int level = 0;
	String mobile = '';
	String nickname = '';
	int pid = 0;
	String profiles = '';
	String realName = '';
	int score = 0;
	int sex = 0;
	bool status = false;
	int tradeStatus = 0;
	int type = 0;

	UserModel();

	factory UserModel.fromJson(Map<String, dynamic> json) => $UserModelFromJson(json);

	Map<String, dynamic> toJson() => $UserModelToJson(this);

	factory UserModel.fromUserData(UserData entity) {
		final model = UserModel();
		model.auth = entity.auth ?? false;
		model.authStatus = entity.authStatus ?? 0;
		model.avatar = entity.avatar ?? '';
		model.countryCode = entity.countryCode ?? '';
		model.email = entity.email ?? '';
		model.fromType = entity.fromType ?? 0;
		model.id = entity.id ?? 0;
		model.idCard = entity.idCard ?? '';
		model.inviteCode = entity.inviteCode ?? '';
		model.isPayment = entity.isPayment ?? false;
		model.level = entity.level ?? 0;
		model.mobile = entity.mobile ?? '';
		model.nickname = entity.nickname ?? '';
		model.pid = entity.pid ?? 0;
		model.profiles = entity.profiles ?? '';
		model.realName = entity.realName ?? '';
		model.score = entity.score ?? 0;
		model.sex = entity.sex ?? 0;
		model.status = entity.status ?? false;
		model.tradeStatus = entity.tradeStatus ?? 0;
		model.type = entity.type ?? 0;
		return model;
	}


	@override
	String toString() {
		return jsonEncode(this);
	}
}