import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/account_info_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/account_info_entity.g.dart';

@JsonSerializable()
class AccountInfo extends Equatable {
  double accountAmount = 0;
  double assetAmount = 0;
  int assetId = 0;
  String currency = '';
  double freezeCash = 0;
  double interestCash = 0;
  double todayWinAmount = 0;
  double todayWinRate = 0;
  double usableCash = 0;
  double profitLoss = 0;

  AccountInfo();

	factory AccountInfo.fromJson(Map<String, dynamic> json) => $AccountInfoFromJson(json);

	Map<String, dynamic> toJson() => $AccountInfoToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  List<Object?> get props => [
		accountAmount,
		assetAmount,
		assetId,
		currency,
		freezeCash,
		interestCash,
		todayWinAmount,
		todayWinRate,
		usableCash,
	];
}



/// 交易详情/持仓信息
@JsonSerializable()
class PositionEntity {
	double appendMargin = 0;
	int availableMargin = 0;
	double buyAvgPrice = 0;
	double buyTotalNum = 0;
	dynamic closeLine;
	double costPrice = 0;
	String createTime = '';
	String currency = '';
	int direction = 0;
	double disableNum = 0;
	double distanceCloseLine = 0;
	double distanceWarningLine = 0;
	double feeAmount = 0;
	double floatingProfitLoss = 0;
	double floatingProfitLossRate = 0;
	int id = 0;
	double marginAmount = 0;
	double marginRatio = 0;
	String market = '';
	double marketValue = 0;
	String orderNo = '';
	int positionDays = 0;
	double positionTotalNum = 0;
	double restNum = 0;
	String securityType = '';
	int stockPrice = 0;
	double stopLossValue = 0;
	String symbol = '';
	String symbolName = '';
	double takeProfitValue = 0;
	int tradeType = 0;
	double tradeUnit = 0;
	int type = 0;
	int warningLine = 0;

	PositionEntity();

	factory PositionEntity.fromJson(Map<String, dynamic> json) => $PositionEntityFromJson(json);

	Map<String, dynamic> toJson() => $PositionEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}