# # # 新版Theme 使用说明 / New Theme Usage Guide

## ## 🎯 目的 / Purpose

为了更清晰地区分 Flutter 默认 Theme 与自定义颜色主题，统一使用以下方式访问：
In order to more clearly distinguish the Flutter default Theme from the custom color theme, the following methods are used to access them:

---

## ## 🎨 context.theme

> 获取当前全局 `ThemeData`（Flutter 默认主题配置）  
> Get current global `ThemeData` (Flutter’s default theme config)

```dart
final theme = context.theme;
final scaffoldColor = theme.scaffoldBackgroundColor;
final shadowColor = theme.shadowColor;
```

---

## ## 🅰 context.textTheme

> 获取当前全局 `TextTheme`（文字样式配置）  
> Get current global `TextTheme` (typography styles)

```dart
final textTheme = context.textTheme;
textTheme.primary.fs16.w500;
textTheme.primary.ffAkz;
```

light 		w300
normal 		w400
medium 		w500
semiBold    w600
bold 		w700
extraBold  	w800

---

## ## 🌈 context.colorTheme

> 获取自定义颜色主题 `CustomColorTheme`（独立于 ThemeData）  
> Get custom color palette `CustomColorTheme` (separate from `ThemeData`)

```dart
context.colorTheme.stockRed;
context.colorTheme.tabActive;
```

---

## ## 🧩 CustomColorTheme

> 自定义颜色配置，管理非文字相关的主题色，例如按钮色、股票色等  
> Manage custom colors unrelated to typography, such as button/stock colors

支持的字段 / Supported fields:

```dart
textPrimary     // 主要文字 Main text
textRegular     // 常规文字 Secondary text
tabActive       // 当前激活标签文字色 Active tab color
tabInactive     // 非激活标签文字色 Inactive tab color
buttonPrimary   // 主要按钮颜色 Primary button color
buttonSecondary // 次级按钮颜色 Secondary button color
stockRed        // 股票红色 Stock red
stockGreen      // 股票绿色 Stock green
pending         // 中性状态色（如待审核） Pending/neutral state color
```

使用示例 / Usage:

```dart
context.colorTheme.stockRed;
context.colorTheme.buttonPrimary;
```

---

## ## ✏️ CustomTextTheme

> 自定义文字样式封装（封装自 `TextStyle`），用于配合自定义颜色构建文字主题  
> Custom typography theme built from `TextStyle` using `CustomColorTheme`

支持的字段 / Supported fields:

```dart
primary       // 主要文字样式 Primary text style
regular       // 常规文字样式 Regular text style
active        // 高亮/激活文字样式 Active text
buttonPrimary // 按钮文字样式 Button text
stockRed      // 股票红色文字 Stock up text
stockGreen    // 股票绿色文字 Stock down text
pending       // 中性色文字（待审核等） Pending/neutral text
```

调用示例 / Usage:

```dart
context.textTheme.primary.fontSize15;
context.textTheme.stockRed.fontSize16.w600;
```

---

## ## 💡 注意事项 / Notes

- `context.textTheme` 与 `context.colorTheme` 二者配合使用可实现文本样式 + 颜色独立管理
- 所有扩展字段支持主题切换自动适配（如 `CustomColorTheme.gpLightA` / `gpDarkA`）
- 可在 `ThemeData.extensions` 中统一注册：

```dart
extensions: [
  CustomTextTheme.gpLightA,
  CustomColorTheme.gpLightA
],
```

---
