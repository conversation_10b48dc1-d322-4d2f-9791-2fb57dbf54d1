// ignore_for_file: unnecessary_null_comparison
import 'package:flutter/material.dart';

class TCUnderlineTabIndicator extends Decoration {
  const TCUnderlineTabIndicator({
    this.borderSide = const BorderSide(width: 2.0, color: Colors.white),
    this.insets = EdgeInsets.zero,
    this.indicatorBottom = 0.0,
    this.indicatorWidth = 28,
    this.indicatorHeight = 4, // This will now be used for actual height
    this.isRound = false,
  })  : assert(borderSide != null),
        assert(insets != null);

  final BorderSide borderSide;
  final EdgeInsetsGeometry insets;
  final double indicatorBottom; // 自定义指示条距离底部距离
  final double indicatorWidth; // 自定义指示条宽度
  final double indicatorHeight; // 自定义指示条高度 - NOW ACTUALLY USED!
  final bool? isRound; // 自定义指示条是否是圆角

  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is TCUnderlineTabIndicator) {
      return TCUnderlineTabIndicator(
        borderSide: BorderSide.lerp(a.borderSide, borderSide, t),
        insets: EdgeInsetsGeometry.lerp(a.insets, insets, t)!,
        indicatorBottom: a.indicatorBottom + (indicatorBottom - a.indicatorBottom) * t,
        indicatorWidth: a.indicatorWidth + (indicatorWidth - a.indicatorWidth) * t,
        indicatorHeight: a.indicatorHeight + (indicatorHeight - a.indicatorHeight) * t,
        isRound: t < 0.5 ? a.isRound : isRound,
      );
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is TCUnderlineTabIndicator) {
      return TCUnderlineTabIndicator(
        borderSide: BorderSide.lerp(borderSide, b.borderSide, t),
        insets: EdgeInsetsGeometry.lerp(insets, b.insets, t)!,
        indicatorBottom: indicatorBottom + (b.indicatorBottom - indicatorBottom) * t,
        indicatorWidth: indicatorWidth + (b.indicatorWidth - indicatorWidth) * t,
        indicatorHeight: indicatorHeight + (b.indicatorHeight - indicatorHeight) * t,
        isRound: t < 0.5 ? isRound : b.isRound,
      );
    }
    return super.lerpTo(b, t);
  }

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _UnderlinePainter(this, onChanged, isRound ?? false);
  }

  Rect _indicatorRectFor(Rect rect, TextDirection textDirection) {
    assert(rect != null);
    assert(textDirection != null);
    final Rect indicator = insets.resolve(textDirection).deflateRect(rect);

    // 取中间坐标
    double cw = (indicator.left + indicator.right) / 2;

    // FIXED: Now using indicatorHeight instead of borderSide.width for height
    Rect indicatorRect = Rect.fromLTWH(
        cw - indicatorWidth / 2,
        indicator.bottom - indicatorHeight - indicatorBottom, // Use indicatorHeight here
        indicatorWidth,
        indicatorHeight // Use indicatorHeight here instead of borderSide.width
    );
    return indicatorRect;
  }

  @override
  Path getClipPath(Rect rect, TextDirection textDirection) {
    return Path()..addRect(_indicatorRectFor(rect, textDirection));
  }
}

class _UnderlinePainter extends BoxPainter {
  _UnderlinePainter(this.decoration, VoidCallback? onChanged, this.isRound)
      : assert(decoration != null),
        super(onChanged);

  final TCUnderlineTabIndicator decoration;
  bool isRound = false;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration != null);
    assert(configuration.size != null);
    final Rect rect = offset & configuration.size!;
    final TextDirection textDirection = configuration.textDirection!;

    // FIXED: Don't deflate by borderSide.width since we're not using it for height anymore
    final Rect indicator = decoration._indicatorRectFor(rect, textDirection);

    // Create paint with the border color
    final Paint paint = Paint()
      ..color = decoration.borderSide.color
      ..style = PaintingStyle.fill; // Use fill instead of stroke for better control

    if (isRound) {
      // For rounded indicator, draw a rounded rectangle
      final RRect roundedRect = RRect.fromRectAndRadius(
        indicator,
        Radius.circular(decoration.indicatorHeight / 2), // Use half height for radius
      );
      canvas.drawRRect(roundedRect, paint);
    } else {
      // For square indicator, draw a simple rectangle
      canvas.drawRect(indicator, paint);
    }
  }
}
