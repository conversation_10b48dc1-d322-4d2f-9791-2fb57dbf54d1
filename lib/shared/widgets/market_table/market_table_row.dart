import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';

import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

import '../../models/route_arguments/trading_arguments.dart';
import '../../models/stock/stock_response.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class MarketTableRow extends StatelessWidget {
  const MarketTableRow({
    super.key,
    required this.data,
    required this.tabType,
  });

  final StockItem data;
  final TradeTabType tabType;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _handleRowTap(context),
        borderRadius: BorderRadius.circular(8.gr),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.gh, horizontal: 4.gw),
          child: Stack(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: _buildNameAndSymbol(context),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Expanded(
                  flex: 3,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Spacer(),
                      _buildLatestPrice(context),
                      20.horizontalSpace,
                      _buildChangePercentage(context),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleRowTap(BuildContext context) {
    context.verifyAuth(
      () => Navigator.pushNamed(
        context,
        routeTradingCenter,
        arguments: TradingArguments(
          instrumentInfo: data.instrumentInfo,
          selectedIndex: tabType.index,
        ),
      ),
    );
  }

  Widget _buildNameAndSymbol(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            data.name ?? 'N/A',
            style: context.textTheme.primary.fs12.w500.copyWith(
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 3,
          ),
          4.verticalSpace,
          Row(
            children: [
              SymbolChip(
                name: data.market ?? '',
                chipColor: context.theme.primaryColor,
              ),
              5.horizontalSpace,
              Flexible(
                child: Text(
                  data.symbol ?? 'N/A',
                  style: context.textTheme.regular.fs12.w500.ffAkz.copyWith(
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLatestPrice(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: FlipText(
        data.latestPrice ?? 0,
        fractionDigits: 3,
        style: context.textTheme.primary.w500.copyWith(
          fontFamily: 'Akzidenz-Grotesk',
          color: (data.gain ?? 0.00).getValueColor(context),
          fontSize: 13.gsp,
        ),
      ),
    );
  }

  Widget _buildChangePercentage(BuildContext context) {
    final gainValue = (data.gain ?? 0) * 100;
    final isPositive = gainValue > 0;

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(
          minWidth: 60.gw, // Minimum width to ensure consistency
          maxWidth: 80.gw, // Maximum width to prevent overflow
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 8.gw,
          vertical: 6.gh,
        ),
        decoration: BoxDecoration(
          color: (data.gain ?? 0.00).getValueColor(context),
          borderRadius: BorderRadius.circular(4.gr),
        ),
        child: Center(
          child: FlipText(
            gainValue.abs(),
            prefix: isPositive ? '+' : '-',
            suffix: '%',
            style: context.textTheme.primary.w700.copyWith(
              color: Colors.white,
              fontSize: 12.gsp, // Slightly smaller to fit better
              fontFamily: 'Akzidenz-Grotesk',
            ),
          ),
        ),
      ),
    );
  }
}
