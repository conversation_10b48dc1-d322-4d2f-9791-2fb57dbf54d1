import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

void setupRSYPConfig() {
  AppConfig(
    flavor: Flavor.rsyp,
    appName: '荣顺优配',
    siteId: "1",
    skinStyle: AppSkinStyle.kGP,
    colorSchemeStyle: ColorSchemeStyle.kDefault,
    environment: "prod",
    baseUrl: 'https://gppro.club',
    marketWsUrl: 'wss://gppro.club',
    inviteLinkUrl: 'https://gppro.club/#/?inviteCode=',
    needShowFutureTrade: true,
    // AES encryption key for RSYP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaKey: '7650f145f0824ba6973d99d43a99d15c',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/prod/1/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/prod/1/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/prod/1/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/prod/1/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/prod/1/app_api.json",
    ],
  );
}
