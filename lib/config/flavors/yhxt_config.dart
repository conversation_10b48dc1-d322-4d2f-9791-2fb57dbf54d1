import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';

void setupYHXTConfig() {
  AppConfig(
    flavor: Flavor.yhxt,
    appName: '沅和信投',
    siteId: "81",
    environment: "prod",
    baseUrl: 'https://reached.837924.com',
    marketWsUrl: 'wss://reached.837924.com',
    inviteLinkUrl: 'https://reached.837924.com/#/?inviteCode=',
    needShowFutureTrade: false,
    // Original primary color
    primaryColor: const Color(0xFF1976D2),
    // AES encryption key for RSYP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaKey: '7650f145f0824ba6973d99d43a99d15c',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/prod/81/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/prod/81/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/prod/81/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/prod/81/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/prod/81/app_api.json",
    ],
  );
}
