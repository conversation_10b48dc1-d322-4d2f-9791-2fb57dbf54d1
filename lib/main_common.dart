import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/features/chat/logic/chat/chat_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_cubit.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications/notifications_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/auth_n/auth_n_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/profile/profile_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/my_app.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'core/dependency_injection/injectable.dart';
import 'core/services/user/user_cubit.dart';
import 'features/account/logic/account/account_cubit.dart';
import 'features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'features/activity/logic/activity/activity_cubit.dart';
import 'features/home/<USER>/home/<USER>';
import 'features/home/<USER>/news/news_cubit.dart';
import 'features/main/logic/main/main_cubit.dart';
import 'features/market/logic/market/market_cubit.dart';
import 'features/market/logic/search/search_cubit.dart';
import 'features/profile/logic/mission_center/cubit/mission_activity_cubit.dart';
import 'features/profile/logic/vip/vip_cubit.dart';
import 'features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'features/sign_up/logic/sign_up/sign_up_cubit.dart';
import 'shared/logic/sort_color/sort_color_cubit.dart';
import 'shared/logic/theme/theme_cubit.dart';

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

Future<void> mainCommon() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  configureDependencies();
  await HostUtil().fetchHostFromOss();

  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );
  final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
  AuthUtils.instance.notifyUpdate(token: token);
  runApp(_Translation());
}

class _Translation extends StatelessWidget {
  const _Translation();

  @override
  Widget build(BuildContext context) {
    return EasyLocalization(
      supportedLocales: const [
        Locale('zh', 'CN'),
        Locale('zh', 'TW'),
        Locale('en', 'US'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('en', 'US'),
      startLocale: const Locale('zh', 'CN'),
      child: const _MultiBlocWrapper(),
    );
  }
}

class _MultiBlocWrapper extends StatelessWidget {
  const _MultiBlocWrapper();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => ThemeCubit()),
        BlocProvider(create: (context) => getIt<SignInCubit>()),
        BlocProvider(create: (context) => getIt<UserCubit>()),
        BlocProvider(create: (context) => getIt<ProfileCubit>()),
        BlocProvider(create: (context) => getIt<SignUpCubit>()),
        BlocProvider(create: (context) => getIt<MarketStatusCubit>()..init(), lazy: false),
        BlocProvider(
            create: (context) => getIt<HomeCubit>()
              ..getBannerList()
              ..getAppUpdate()),
        BlocProvider(create: (context) => getIt<AccountCubit>()..getContractSummary()),
        BlocProvider(create: (context) => getIt<AccountScreenCubitV2>()),
        BlocProvider(
          create: (context) => getIt<MarketCubit>()
            ..fetchTableData(sortType: 1, order: 'DESC', isHome: true)
            ..fetchTableData(sortType: 0, order: 'DESC', isHome: true),
        ),
        BlocProvider(create: (context) => getIt<SearchCubit>()),
        BlocProvider(create: (context) => getIt<MainCubit>()),
        BlocProvider(create: (context) => getIt<ActivityCubit>()..getTasks()),
        BlocProvider(create: (context) => getIt<AuthNCubit>()),
        BlocProvider(create: (context) => SortColorCubit()),
        BlocProvider(create: (context) => getIt<HomeNotificationCubit>()..getNotifications()),
        BlocProvider(create: (context) => getIt<NotificationsCubit>()..getNotificationCount()),
        BlocProvider(create: (context) => getIt<IndexTradeCubit>()..init(), lazy: false),
        BlocProvider(create: (context) => ExchangeRateCubit()..fetchExchangeRate(), lazy: false),
        BlocProvider(create: (context) => SysSettingsCubit()..fetchSysSettings(), lazy: false),
        BlocProvider(create: (context) => getIt<WatchListCubit>()),
        BlocProvider(create: (context) => getIt<NewsCubit>()..getNews(), lazy: false),
        BlocProvider(create: (context) => getIt<ChatCubit>()),
        BlocProvider(create: (context) => getIt<MissionActivityCubit>()..getSignInLog(), lazy: false),
        BlocProvider(create: (context) => getIt<VipCubit>()..getUserLevelConfig()),
        BlocProvider(create: (context) => getIt<ThirdPartyChannelCubit>()),
        BlocProvider(create: (context) => getIt<AppInfoCubit>()..getAppInfoList(), lazy: false),
        BlocProvider(create: (context) => getIt<AccountInfoCubit>()..getAccountInfo(), lazy: false),
      ],
      child: MyApp(routeObserver: routeObserver), // Pass routeObserver to MyApp
    );
  }
}
