#!/bin/bash

# switch_skin.sh - A script to switch skins in a Flutter app
# Usage: ./switch_skin.sh [skin_name] [color_scheme]

# Check if both arguments are provided
if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Error: Please provide both skin name and color scheme as arguments"
    echo "Usage: ./switch_skin.sh [skin_name] [color_scheme]"
    echo "Available skins: $(ls -1 assets/skins 2>/dev/null | tr '\n' ' ')"
    if [ -n "$1" ] && [ -d "assets/skins/$1" ]; then
        echo "Available color schemes for '$1': $(ls -1 assets/skins/$1 2>/dev/null | tr '\n' ' ')"
    fi
    exit 1
fi

SKIN="$1"
COLOR_SCHEME="$2"
SKIN_DIR="assets/skins/$SKIN"
COLOR_SCHEME_DIR="$SKIN_DIR/$COLOR_SCHEME"
TARGET_DIR="assets"

# Check if skin directory exists
if [ ! -d "$SKIN_DIR" ]; then
    echo "Error: Skin '$SKIN' not found in assets/skins/"
    echo "Available skins: $(ls -1 assets/skins 2>/dev/null | tr '\n' ' ')"
    exit 1
fi

# Check if color scheme directory exists
if [ ! -d "$COLOR_SCHEME_DIR" ]; then
    echo "Error: Color scheme '$COLOR_SCHEME' not found in $SKIN_DIR/"
    echo "Available color schemes for '$SKIN': $(ls -1 $SKIN_DIR 2>/dev/null | tr '\n' ' ')"
    exit 1
fi

echo "Switching to skin: $SKIN with color scheme: $COLOR_SCHEME"
echo "Copying files from $COLOR_SCHEME_DIR to $TARGET_DIR"

# Define the asset folders to copy
ASSET_FOLDERS=("icons" "images" "svg")

# Copy each asset folder
for folder in "${ASSET_FOLDERS[@]}"; do
    source_folder="$COLOR_SCHEME_DIR/$folder"
    target_folder="$TARGET_DIR/$folder"
    
    if [ -d "$source_folder" ]; then
        echo "Copying $folder from $source_folder to $target_folder"
        
        # Create target directory if it doesn't exist
        mkdir -p "$target_folder"
        
        # Copy all files from source folder to target folder
        find "$source_folder" -type f | while read -r file; do
            # Get the relative path within the source folder
            rel_path="${file#$source_folder/}"
            
            # Create target subdirectory if it doesn't exist
            target_subdir="$target_folder/$(dirname "$rel_path")"
            mkdir -p "$target_subdir"
            
            # Copy the file (will replace if exists)
            cp -v "$file" "$target_folder/$rel_path"
        done
    else
        echo "Warning: $folder directory not found in $COLOR_SCHEME_DIR, skipping"
    fi
done

echo "Skin switched to '$SKIN' with color scheme '$COLOR_SCHEME' successfully!"